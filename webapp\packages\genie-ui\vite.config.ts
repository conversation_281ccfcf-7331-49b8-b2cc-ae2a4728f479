import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// 开发环境的代理配置，用于独立开发调试
// const serverBaseUrl = 'http://127.0.0.1:9080';
const serverBaseUrl = 'https://***********:13000';

export default defineConfig(({ mode }) => ({
  plugins: [react()],
  resolve: {
    alias: {
      '@': './src',
      crypto: 'crypto-browserify',
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3001,
    proxy: {
      '/web': {
        target: serverBaseUrl,
        changeOrigin: true,
      },
      // 兼容 HChatData 的 API 路径
      '/api': {
        target: serverBaseUrl,
        changeOrigin: true,
      }
    },
  },
  define: {
    // 开发环境使用代理，生产环境使用完整URL
    SERVICE_BASE_URL: JSON.stringify(mode === 'development' ? '' : serverBaseUrl),
  },
  build: {
    lib: {
      entry: './src/index.ts',
      name: 'Genie<PERSON>',
      formats: ['es', 'umd'],
      fileName: (format: string) => `index.${format}.js`
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'antd',
        '@ant-design/icons',
        'dayjs',
        'lodash',
        'classnames',
        'ahooks',
        'react-markdown',
        'react-syntax-highlighter',
        'remark-gfm'
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          antd: 'antd',
          '@ant-design/icons': 'AntdIcons',
          dayjs: 'dayjs',
          lodash: '_',
          classnames: 'classNames',
          ahooks: 'ahooks',
          'react-markdown': 'ReactMarkdown',
          'react-syntax-highlighter': 'ReactSyntaxHighlighter',
          'remark-gfm': 'remarkGfm'
        },
        assetFileNames: (assetInfo) => {
          // 处理字体文件，保持原有的目录结构
          if (assetInfo.names && assetInfo.names.length > 0 && /\.(eot|woff2?|ttf|svg)$/.test(assetInfo.names[0])) {
            return 'relayFonts/[name][extname]';
          }
          return 'assets/[name].[hash][extname]';
        }
      }
    },
    // 确保字体文件被包含在构建中
    assetsInclude: ['**/*.eot', '**/*.woff', '**/*.woff2', '**/*.ttf']
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  }
}));
