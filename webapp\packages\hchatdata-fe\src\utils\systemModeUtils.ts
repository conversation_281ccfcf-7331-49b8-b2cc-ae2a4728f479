/**
 * 系统模式相关的工具函数
 */

export type SystemMode = 'frontend' | 'backend';

/**
 * 获取当前系统模式
 */
export const getCurrentSystemMode = (): SystemMode => {
  return (localStorage.getItem('systemMode') as SystemMode) || 'frontend';
};

/**
 * 设置系统模式
 */
export const setCurrentSystemMode = (mode: SystemMode): void => {
  localStorage.setItem('systemMode', mode);
};

/**
 * 判断当前路径是否为管理页面
 */
export const isAdminPath = (pathname: string): boolean => {
  const adminPaths = ['/agent', '/plugin', '/model', '/metric', '/database', '/llm', '/system', '/user'];
  return adminPaths.some(path => pathname.startsWith(path));
};

/**
 * 判断当前路径是否为前台页面
 */
export const isFrontendPath = (pathname: string): boolean => {
  return pathname === '/chat' || pathname.startsWith('/chat/');
};

/**
 * 根据系统模式获取默认重定向路径
 */
export const getDefaultRedirectPath = (mode: SystemMode): string => {
  return mode === 'backend' ? '/agent' : '/chat';
};
