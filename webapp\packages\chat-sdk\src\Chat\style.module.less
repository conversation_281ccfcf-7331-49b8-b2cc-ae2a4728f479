.chat {
  height: 100%;
  overflow: hidden;
  // 蓝色系
  // background: #F8F8FB;
  // background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);
  // background: linear-gradient(90deg, #FEFFFF 0%, #F4FBFF 33%, #EFF8FF 66%, #EEF4FF 100%);
  // background: linear-gradient(to top right, #FEFFFF 0%, #F4FBFF 33%, #EFF8FF 66%, #EEF4FF 100%);
  background: linear-gradient(45deg, #FFFFFF 0%, #F8FBFF 33%, #F3F7FF 66%, #EEF4FF 100%);
  // agentListToggle

  // 红色系
  // background:
  //         linear-gradient(180deg, rgba(204, 0, 0, 0) 29.44%, rgba(204, 0, 0, 0.06) 100%),
  //         linear-gradient(90deg, #f7f3f3 0%, #f7f3f3 20%, #f9ebeb 60%, #f7f3f3 80%, #f7f3f3 100%);

  //灰色系
  // background:
  //        linear-gradient(180deg, rgba(128, 128, 128, 0) 29.44%, rgba(128, 128, 128, 0.06) 100%),
  //        linear-gradient(90deg, #f3f3f3 0%, #f3f3f3 20%, #ebebeb 60%, #f3f3f3 80%, #f3f3f3 100%);

  .chatSection {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative; /* 为下拉框提供定位上下文 */

    .chatApp {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
      color: rgba(0, 0, 0, 0.87);

      .chatBody {
        display: flex;
        flex: 1;
        height: 100%;

        .chatContent {
          position: relative;
          display: flex;
          flex-direction: column;
          width: 100%;
          height: 100%;

          .chatHeader {
            position: absolute;
            top: 0;
            z-index: 9;
            display: flex;
            align-items: baseline;
            width: 100%;
            padding: 14px 16px;
            background: rgba(243, 243, 247, 0.85);
            backdrop-filter: blur(2px);

            .chatHeaderTitle {
              color: var(--text-color);
              font-weight: 500;
              font-size: 15px;
            }

            .chatHeaderTip {
              max-width: 600px;
              margin-left: 5px;
              overflow: hidden;
              color: var(--text-color-third);
              font-size: 12px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  &.historyVisible {
    .chatSection {
      .chatApp {
        width: calc(100% - 707px);
      }
    }
  }

  &.mobile {
    .chatSection {
      .chatApp {
        width: 100%;
      }
    }
  }
}

.showCaseModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;

      .ant-modal-header {
        border-radius: 8px 8px 0 0;
      }

      .ant-modal-body {
        padding: 20px 0 !important;
      }
    }
  }
}

.showCaseDrawer {
  :global {
    .ant-drawer-content {
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      .ant-drawer-body {
        padding: 4px 0 !important;
      }
    }
  }
}

:global {
  .ss-chat-recommend-options {
    .ant-table-thead .ant-table-cell {
      padding: 8px !important;
    }

    .ant-table-tbody .ant-table-cell {
      padding: 8px !important;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.agentListContainer {
  display: flex;
  height: 100%;
  position: relative;
  flex-shrink: 0; /* 防止容器收缩 */
  overflow: visible; /* 确保下拉框可以正常显示 */
}

.agentListToggle {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  position: absolute;
  left: 100%;
  top: 30px;
  transform: translateY(-50%);
  z-index: 10;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.06);

    .anticon {
      color: #374151 !important;
    }
  }

  &:active {
    background: rgba(0, 0, 0, 0.1);
    transform: translateY(-50%) scale(0.95);
  }

  .anticon {
    font-size: 14px !important;
    color: #9ca3af !important;
    transition: all 0.2s ease;
  }
}
