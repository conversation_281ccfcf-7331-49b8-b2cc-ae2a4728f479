import { format } from 'sql-formatter';
import { exportTextFile } from './utils';

interface Schema {
  fieldNameList?: string[];
  values?: Array<{ fieldName: string; fieldValue: string }>;
}

interface Term {
  name: string;
  alias?: string[];
  description: string;
}

interface SqlInfoType {
  parsedS2SQL?: string;
  correctedS2SQL?: string;
  querySQL?: string;
}

interface LlmReq {
  schema?: Schema;
  terms?: Term[];
  priorExts?: string;
}

interface LlmResp {
  sqlRespMap?: Record<string, { fewShots?: Array<{
    question: string;
    sql: string;
  }> }>;
}

/**
 * 获取Schema映射文本
 */
export const getSchemaMapText = (llmReq: LlmReq): string => {
  const { schema, terms, priorExts } = llmReq || {};
  
  return `
Schema映射
${schema?.fieldNameList?.length ? `名称：${schema.fieldNameList.join('、')}` : ''}${
    schema?.values?.length
      ? `
取值：${schema.values
          .map((item) => `${item.fieldName}: ${item.fieldValue}`)
          .join('、')}`
      : ''
  }${
    priorExts
      ? `
附加：${priorExts}`
      : ''
  }${
    terms?.length
      ? `
术语：${terms
          .map((item) => `${item.name}${item.alias?.length ? `(${item.alias.join(',')})` : ''}: ${item.description}`)
          .join('、')}`
      : ''
  }

`;
};

/**
 * 获取Few-shot示例文本
 */
export const getFewShotText = (llmResp: LlmResp): string => {
  const fewShots = (Object.values(llmResp?.sqlRespMap || {})[0]?.fewShots || []);
  
  return `
Few-shot示例${fewShots
    .map((item, index) => `

示例${index + 1}：
问题：${item.question}
SQL：
${format(item.sql)}
`)
    .join('')}
`;
};

/**
 * 获取解析S2SQL文本
 */
export const getParsedS2SQLText = (sqlInfo: SqlInfoType, queryMode?: string): string => {
  if (!sqlInfo.parsedS2SQL) return '';
  
  return `
${queryMode === 'LLM_S2SQL' || queryMode === 'PLAIN_TEXT' ? 'LLM' : 'Rule'}解析S2SQL

${format(sqlInfo.parsedS2SQL)}
`;
};

/**
 * 获取修正S2SQL文本
 */
export const getCorrectedS2SQLText = (sqlInfo: SqlInfoType): string => {
  if (!sqlInfo.correctedS2SQL) return '';
  
  return `
修正S2SQL

${format(sqlInfo.correctedS2SQL)}
`;
};

/**
 * 获取最终执行SQL文本
 */
export const getQuerySQLText = (sqlInfo: SqlInfoType): string => {
  if (!sqlInfo.querySQL) return '';
  
  return `
最终执行SQL

${format(sqlInfo.querySQL)}
`;
};

/**
 * 获取错误信息文本
 */
export const getErrorMsgText = (executeErrorMsg: string): string => {
  if (!executeErrorMsg) return '';
  
  return `
异常日志

${executeErrorMsg}
`;
};

/**
 * 导出日志文件
 */
export const onExportLog = (
  question: string,
  llmReq: LlmReq,
  llmResp: LlmResp,
  sqlInfo: SqlInfoType,
  executeErrorMsg: string,
  agentId?: number,
  queryId?: number,
  queryMode?: string
) => {
  let text = '';
  
  if (question) {
    text += `
问题：${question}
`;
  }
  
  if (llmReq) {
    text += getSchemaMapText(llmReq);
  }
  
  const fewShots = (Object.values(llmResp?.sqlRespMap || {})[0] as any)?.fewShots || [];
  if (fewShots.length > 0) {
    text += getFewShotText(llmResp);
  }
  
  if (sqlInfo.parsedS2SQL) {
    text += getParsedS2SQLText(sqlInfo, queryMode);
  }
  
  if (sqlInfo.correctedS2SQL) {
    text += getCorrectedS2SQLText(sqlInfo);
  }
  
  if (sqlInfo.querySQL) {
    text += getQuerySQLText(sqlInfo);
  }
  
  if (executeErrorMsg) {
    text += getErrorMsgText(executeErrorMsg);
  }

  exportTextFile(text, `GoData-debug-${agentId}-${queryId}.log`)
}