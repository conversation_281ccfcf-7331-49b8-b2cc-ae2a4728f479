!(function(){var Ds=Object.defineProperty,Os=Object.defineProperties;var Us=Object.getOwnPropertyDescriptors;var ue=Object.getOwnPropertySymbols;var At=Object.prototype.hasOwnProperty,vt=Object.prototype.propertyIsEnumerable;var gt=(F,S,v)=>S in F?Ds(F,S,{enumerable:!0,configurable:!0,writable:!0,value:v}):F[S]=v,O=(F,S)=>{for(var v in S||(S={}))At.call(S,v)&&gt(F,v,S[v]);if(ue)for(var v of ue(S))vt.call(S,v)&&gt(F,v,S[v]);return F},K=(F,S)=>Os(F,Us(S));var jt=(F,S)=>{var v={};for(var s in F)At.call(F,s)&&S.indexOf(s)<0&&(v[s]=F[s]);if(F!=null&&ue)for(var s of ue(F))S.indexOf(s)<0&&vt.call(F,s)&&(v[s]=F[s]);return v};var xe=(F,S,v)=>new Promise((s,A)=>{var pe=Z=>{try{q(v.next(Z))}catch($){A($)}},he=Z=>{try{q(v.throw(Z))}catch($){A($)}},q=Z=>Z.done?s(Z.value):Promise.resolve(Z.value).then(pe,he);q((v=v.apply(F,S)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[440],{69172:function(F,S,v){"use strict";v.r(S)},6531:function(F,S,v){"use strict";v.d(S,{Xo:function(){return ft}});var s=v(31549),A=v(44194),pe=v(46504),he=v(34284),q=v(16156),Z=v(31063),$=v(78736),Et=v(25016),_e=v(15783),wt=v(44319),kt=v(77477),Nt=v(66087),yt=v(27634),Ct=v(62568),oe=v(37586),De=v(13166),Oe=v(84603),Ue=v(97862),Mt=v(92759),It=v(85475),Tt=v(60808),bt=v(48893),Bt=v(4711),Rt=v(51947),Pt=v(49683),_t=v(67229),Le=v(60394),Dt=v(98359),Ot=v(1177),Ut=v(51865),P=v.n(Ut),te=v(39378),Ls=v.n(te),Lt=v(13706),Ft=v(8140),G=v(18258),fe=v(19629),Fe=v(76481),ge=v(96073),St=v(36657),Kt=v(92313),Zt=v(6103),Qt=v(90059),Jt=v(90228),Se=v(63705),zt=v(57910),Wt=v.n(zt),Ht=v(28977),Xt=v.n(Ht),Vt=v(95234);const Ke=a=>{if(!a)return!1;try{return JSON.parse(a),!0}catch(n){return!1}},Ze=a=>{try{const n=new DOMParser().parseFromString(a,"text/html");return Array.from(n.body.childNodes).some(l=>l.nodeType===Node.ELEMENT_NODE)}catch(n){return!1}},Qe=()=>`${Date.now()}-${Math.floor(1e4*Math.random())}`,ie=a=>{a&&window.open(a)},Je=864e5,ze=a=>{const n=Date.now(),l=new Date(Number(a)),i=n-l.getTime();return i<=Je?l.toLocaleString():i<864e6?`${Math.ceil(i/Je)}\u5929\u524D`:"10\u5929\u524D"},Fs=(a,n)=>{const l=new URL(a);return new URLSearchParams(l.search).get(n)},Gt=a=>{setTimeout(()=>{a.scrollTop=a.scrollHeight},100)},We=(0,te.throttle)(a=>{if(navigator.clipboard)navigator.clipboard.writeText((a||"")+"");else{const n=document.createElement("textarea");n.value=a+"",document.body.appendChild(n),n.focus(),n.select(),document.execCommand("copy"),document.body.removeChild(n)}},1e3),Ss=a=>{var n,l;(n=a.stopPropagation)==null||n.call(a),(l=a.preventDefault)==null||l.call(a)},Yt=(a,n)=>{if(!a)return;const l=document.createElement("a");l.href=a,l.style.display="none",l.target="_blank",n&&(l.download=n),document.body.appendChild(l),l.click(),document.body.removeChild(l)};function qt(a){return`${Math.floor(a/60)}:${Math.floor(a%60).toString().padStart(2,"0")}`}const $t=()=>`session-${Qe()}`;let He=null;const Ks=a=>{He=a},se=()=>He,es=()=>{const a=navigator.userAgent.toLowerCase();return a.includes("windows")?"Windows":a.includes("macintosh")||a.includes("mac os x")?"Mac":"Unknown"},ts=a=>{const n=[],l=a.split(`
`);let i=[];for(const o in l)if(typeof l[o]=="string"&&l[o].length>0&&`${l[o]}`.trim().length>0){const f=l[o].split(",");if(Number(o)===0)i=f;else{const d={};for(let m=0;m<i.length;m++){const r=String(i[m]);if(i[m]&&!d[r])try{f[m]?d[r]=String(f[m]):d[r]=""}catch(u){console.error(u),d[r]=""}}n.push(d)}}return n};var H;(function(a){a.file="file",a.browser="browser",a.follow="follow"})(H||(H={}));const{TextArea:ss}=pe.default,Xe=a=>{const{placeholder:n,showBtn:l,disabled:i,product:o,send:f}=a,[d,m]=(0,A.useState)(""),[r,u]=(0,A.useState)(!1),h=(0,A.useRef)(null),I=(0,A.useRef)({}),C=(0,A.useMemo)(()=>`\u23CE\u53D1\u9001\uFF0C${es()==="Mac"?"\u2318":"^"} + \u23CE \u6362\u884C`,[]);return(0,s.jsx)("div",{className:l?"rounded-[12px] bg-[linear-gradient(to_bottom_right,#4040ff,#ff49fd,#d763fc,#3cc4fa)] p-[2px]":"",children:(0,s.jsxs)("div",{className:"rounded-[12px] border border-[#E9E9F0] overflow-hidden p-[12px] bg-[#fff]",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(ss,{ref:h,value:d,placeholder:n,rows:3,autoSize:{minRows:3,maxRows:10},className:P()("h-62 no-border-textarea no-resize-textarea border-0 resize-none p-[0px] focus:border-0 bg-[#fff]"),style:l&&o?{textIndent:"86px"}:{},onChange:E=>{m(E.target.value)},onPressEnter:()=>{var E,w;if(!I.current.compositing){if(I.current.cmdPress){const p=(w=(E=h.current)==null?void 0:E.resizableTextArea)==null?void 0:w.textArea;if(!p)return;const{selectionStart:k,selectionEnd:M}=p||{},N=d.substring(0,k)+`
`+d.substring(M);return m(N),void setTimeout(()=>{p.selectionStart=k+1,p.selectionEnd=k+1,p.focus()},20)}d&&!i&&(f({message:d,outputStyle:o==null?void 0:o.type,deepThink:r}),m(""))}},onKeyDown:E=>{I.current.cmdPress=E.metaKey||E.ctrlKey},onKeyUp:()=>{I.current.cmdPress=!1},onCompositionStart:()=>{I.current.compositing=!0},onCompositionEnd:()=>{I.current.compositing=!1}}),l&&o?(0,s.jsx)("div",{className:"h-[24px] w-[80px] absolute top-0 left-0 flex items-center justify-center rounded-[6px] bg-[#f4f4f9] text-[12px] ",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:`text-14 ${o.color} flex items-center`,children:(g=o.img,{"icon-diannao":(0,s.jsx)(oe.Z,{}),"icon-wendang":(0,s.jsx)(De.Z,{}),"icon-ppt":(0,s.jsx)(Oe.Z,{}),"icon-biaoge":(0,s.jsx)(Ue.Z,{})}[g]||(0,s.jsx)(oe.Z,{}))}),(0,s.jsx)("div",{className:"ml-[6px]",children:o.name})]})}):null]}),(0,s.jsxs)("div",{className:"h-30 flex justify-between items-center mt-[6px]",children:[l?(0,s.jsxs)(he.ZP,{color:r?"primary":"default",variant:"outlined",className:P()("text-[12px] p-[8px] h-[28px] transition-all hover:text-[#333] hover:bg-[rgba(64,64,255,0.02)] hover:border-[rgba(64,64,255,0.2)]"),onClick:()=>{u(!r)},children:[(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z",fill:"currentColor",stroke:"currentColor","stroke-width":".1"}),(0,s.jsx)("path",{d:"M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z",fill:"currentColor",stroke:"currentColor","stroke-width":".2"})]}),(0,s.jsx)("span",{className:"ml-[-4px]",children:"\u6DF1\u5EA6\u7814\u7A76"})]}):(0,s.jsx)("div",{}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-[12px] text-gray-300 mr-8 flex items-center",children:C}),(0,s.jsx)(q.Z,{title:"\u53D1\u9001",children:(0,s.jsx)("i",{className:"font_family icon-fasongtianchong "+(!d||i?"cursor-not-allowed text-[#ccc] pointer-events-none":"cursor-pointer"),onClick:()=>{f({message:d,outputStyle:o==null?void 0:o.type,deepThink:r}),m("")}})})]})]})]})});var g},as={v:"5.6.9",fr:25,ip:0,op:75,w:400,h:240,nm:"genie",ddd:0,assets:[{id:"comp_0",layers:[{ddd:0,ind:1,ty:4,nm:"star",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[10]},{t:50,s:[56]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:0,s:[281.75,75.25,0],to:[0,0,0],ti:[-10.5,-17.5,0]},{i:{x:.833,y:.833},o:{x:.167,y:.167},t:14.254,s:[310.25,89,0],to:[9.75,-8.75,0],ti:[-6,-7,0]},{i:{x:.833,y:.833},o:{x:.167,y:.167},t:23.211,s:[349.75,83.25,0],to:[7.5,-32,0],ti:[21.043,-31.565,0]},{t:50,s:[281.75,75.125,0]}],ix:2},a:{a:0,k:[114.891,76.501,0],ix:1},s:{a:0,k:[14,14,100],ix:6}},ao:1,shapes:[{ty:"sr",sy:1,d:1,pt:{a:0,k:4,ix:3},p:{a:0,k:[114.891,76.501],ix:4},r:{a:0,k:0,ix:5},ir:{a:0,k:38,ix:6},is:{a:0,k:0,ix:8},or:{a:0,k:100,ix:7},os:{a:0,k:0,ix:9},ix:1,nm:"xing xing",mn:"ADBE Vector Shape - Star",hd:!1},{ty:"fl",c:{a:0,k:[.243137269862,.470588265213,.992156922583,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"fill 2",mn:"ADBE Vector Graphic - Fill",hd:!1}],ip:0,op:75,st:0,bm:0}]}],layers:[{ddd:0,ind:2,ty:0,nm:"star",refId:"comp_0",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.333,y:0},t:50,s:[200,120,0],to:[0,0,0],ti:[0,0,0]},{i:{x:.833,y:.833},o:{x:.333,y:0},t:52,s:[200,114,0],to:[0,0,0],ti:[0,0,0]},{t:54,s:[200,120,0]}],ix:2},a:{a:0,k:[200,120,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:400,h:240,ip:0,op:75,st:0,bm:0},{ddd:0,ind:3,ty:4,nm:'"Genie 2" lunkuo',sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[199.5,158.5,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[-7.61,7.684],[0,11.678],[.3,1.734],[0,0],[0,0],[0,0],[4.444,-3.314],[5.876,0],[4.896,5.35],[0,8.89],[-5.124,5.5],[-7.76,0],[-4.144,-2.26],[-2.26,-3.39],[0,0],[14.99,.302],[8.136,-7.984],[.3,-13.484],[-7.91,-8.06],[-12.204,-.226]],o:[[7.608,-7.684],[0,-2.862],[0,0],[0,0],[0,0],[-1.282,5.802],[-4.294,3.24],[-7.76,0],[-5.048,-5.498],[0,-8.964],[5.046,-5.424],[4.368,0],[3.992,2.186],[0,0],[-8.062,-11.526],[-12.204,.152],[-8.062,7.91],[.3,13.41],[7.984,8.06],[11.826,0]],v:[[-96.05,-6.667],[-84.637,-35.708],[-85.089,-42.601],[-123.396,-42.601],[-123.396,-28.476],[-100.909,-28.476],[-109.497,-14.803],[-124.752,-9.944],[-143.736,-17.967],[-151.307,-39.55],[-143.623,-61.246],[-124.413,-69.382],[-111.644,-65.992],[-102.265,-57.63],[-90.174,-66.331],[-124.752,-84.072],[-155.262,-71.868],[-167.805,-39.776],[-155.488,-7.571],[-125.204,4.859]],c:!0},ix:2},nm:"G",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"G",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[-3.39,2.26],[-4.898,0],[-2.864,-2.108],[-.452,-4.294],[0,0]],o:[[3.088,-2.108],[4.142,0],[3.164,2.336],[0,0],[.452,-4.368]],v:[[-55.37,-46.556],[-43.392,-49.72],[-32.883,-46.556],[-27.459,-36.612],[-61.133,-36.612]],c:!0},ix:2},nm:"e",mn:"ADBE Vector Shape - Group",hd:!1},{ind:1,ty:"sh",ix:2,ks:{a:0,k:{i:[[-6.932,9.19],[0,0],[7.306,0],[3.238,2.11],[.526,5.576],[0,0],[0,1.056],[5.8,6.028],[10.018,0],[6.026,-6.402],[0,-10.396],[-5.952,-5.95],[-11.3,0]],o:[[0,0],[-4.596,6.102],[-5.726,0],[-3.768,-2.486],[0,0],[.226,-2.938],[0,-10.018],[-5.802,-6.026],[-10.396,0],[-5.952,6.254],[0,10.396],[6.176,6.176],[11.826,0]],v:[[-14.012,-8.927],[-23.504,-18.306],[-41.358,-9.153],[-54.805,-12.317],[-61.246,-24.408],[-11.639,-24.408],[-11.3,-30.397],[-20.001,-54.466],[-43.731,-63.506],[-68.365,-53.901],[-77.292,-28.928],[-68.365,-4.407],[-42.149,4.859]],c:!0},ix:2},nm:"e",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"mm",mm:1,nm:"\u5408\u5E76\u8DEF\u5F84 1",mn:"ADBE Vector Filter - Merge",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"e",np:5,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[-2.938,2.864],[-4.294,0],[-2.336,-2.486],[0,-4.594],[0,0],[0,0],[0,0],[4.294,4.446],[7.984,0],[3.464,-1.808],[1.958,-3.314],[0,0],[0,0],[0,0]],o:[[0,0],[0,-4.368],[2.862,-2.786],[4.67,0],[2.26,2.412],[0,0],[0,0],[0,0],[0,-8.888],[-4.22,-4.368],[-3.918,0],[-3.692,1.884],[0,0],[0,0],[0,0],[0,0]],v:[[15.255,3.39],[15.255,-35.143],[19.662,-45.991],[30.397,-50.172],[40.906,-46.443],[44.296,-35.934],[44.296,3.39],[59.551,3.39],[59.551,-36.951],[53.11,-56.952],[34.804,-63.506],[23.73,-60.794],[15.255,-52.997],[15.255,-62.037],[0,-62.037],[0,3.39]],c:!0},ix:2},nm:"n",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"n",np:3,cix:2,bm:0,ix:3,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0]],v:[[90.174,3.39],[90.174,-62.037],[74.919,-62.037],[74.919,3.39]],c:!0},ix:2},nm:"i",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"mm",mm:1,nm:"\u5408\u5E76\u8DEF\u5F84 1",mn:"ADBE Vector Filter - Merge",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"i",np:4,cix:2,bm:0,ix:4,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[-3.39,2.26],[-4.898,0],[-2.864,-2.108],[-.452,-4.294],[0,0]],o:[[3.088,-2.108],[4.142,0],[3.164,2.336],[0,0],[.452,-4.368]],v:[[124.413,-46.556],[136.391,-49.72],[146.9,-46.556],[152.324,-36.612],[118.65,-36.612]],c:!0},ix:2},nm:"e",mn:"ADBE Vector Shape - Group",hd:!1},{ind:1,ty:"sh",ix:2,ks:{a:0,k:{i:[[-6.932,9.19],[0,0],[7.306,0],[3.238,2.11],[.526,5.576],[0,0],[0,1.056],[5.8,6.028],[10.018,0],[6.026,-6.402],[0,-10.396],[-5.952,-5.95],[-11.3,0]],o:[[0,0],[-4.596,6.102],[-5.726,0],[-3.768,-2.486],[0,0],[.226,-2.938],[0,-10.018],[-5.802,-6.026],[-10.396,0],[-5.952,6.254],[0,10.396],[6.176,6.176],[11.826,0]],v:[[165.771,-8.927],[156.279,-18.306],[138.425,-9.153],[124.978,-12.317],[118.537,-24.408],[168.144,-24.408],[168.483,-30.397],[159.782,-54.466],[136.052,-63.506],[111.418,-53.901],[102.491,-28.928],[111.418,-4.407],[137.634,4.859]],c:!0},ix:2},nm:"e",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"mm",mm:1,nm:"\u5408\u5E76\u8DEF\u5F84 1",mn:"ADBE Vector Filter - Merge",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"e 2",np:5,cix:2,bm:0,ix:5,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[.339,-39.606],ix:2},a:{a:0,k:[.339,-39.606],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"zuhe xingzhuang",np:5,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gf",o:{a:0,k:100,ix:10},r:1,bm:0,g:{p:5,k:{a:0,k:[0,.239,.765,.984,.407,.241,.533,.99,.814,.243,.302,.996,.907,.373,.241,.998,1,.502,.18,1],ix:9}},s:{a:0,k:[-165.568,-47.316],ix:5},e:{a:0,k:[170.432,-22.506],ix:6},t:1,nm:"Gradient Stroke 1",mn:"ADBE Vector Graphic - G-Fill",hd:!1},{ty:"tr",p:{a:0,k:[.339,-39.606],ix:2},a:{a:0,k:[.339,-39.606],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"\u53D8\u6362"}],nm:"group 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:75,st:0,bm:0}],markers:[]},ns=()=>(0,s.jsx)("div",{className:"mb-54",children:(0,s.jsx)(Lt.Z,{options:{loop:!0,autoplay:!0,animationData:as,rendererSettings:{preserveAspectRatio:"xMidYMid slice",className:"lottie"}},height:68,width:200})}),ls={"Content-Type":"application/json","Cache-Control":"no-cache",Connection:"keep-alive",Accept:"text/event-stream"},rs=(a,n)=>{switch(a.messageType){case"plan":(function(l,i){l.taskId||(i.multiAgent.plan=O({taskId:l.taskId},l==null?void 0:l.resultMap))})(a,n);break;case"plan_thought":(function(l,i){i.multiAgent.plan_thought||(i.multiAgent.plan_thought=""),l.resultMap.isFinal?i.multiAgent.plan_thought=l.resultMap.planThought:i.multiAgent.plan_thought+=l.resultMap.planThought})(a,n);break;case"task":(function(l,i){var m;i.multiAgent.tasks||(i.multiAgent.tasks=[]);const o=(f=i.multiAgent.tasks,d=l.taskId,f.findIndex(r=>{var u;return((u=r[0])==null?void 0:u.taskId)===d}));var f,d;(m=l.resultMap)!=null&&m.messageType&&function(r,u,h){const I=r.resultMap.messageType,C=function(g,E,w){var p;return E===-1?-1:(p=g[E])==null?void 0:p.findIndex(k=>k.messageId===w)}(u.multiAgent.tasks,h,r.messageId);switch(I){case"tool_thought":(function(g,E,w,p){const{tasks:k}=E.multiAgent,{taskId:M,resultMap:N}=g,{toolThought:B,isFinal:b}=N;if(w===-1)return void k.push([Ve(M,N)]);if(p===-1)return void k[w].push(Ve(M,N));(function(T,R,D){T.toolThought=D?R:(T.toolThought||"")+R})(k[w][p],B||"",b)})(r,u,h,C);break;case"html":case"markdown":case"ppt":(function(g,E,w,p){var k;w!==-1?p!==-1?g.resultMap.resultMap.isFinal?E.multiAgent.tasks[w][p].resultMap=K(O({},g.resultMap.resultMap),{codeOutput:g.resultMap.resultMap.data}):(E.multiAgent.tasks[w][p].resultMap.isFinal=!1,E.multiAgent.tasks[w][p].resultMap.codeOutput+=((k=g.resultMap.resultMap)==null?void 0:k.data)||""):(g.resultMap.resultMap=Ge(g.resultMap.resultMap),E.multiAgent.tasks[w].push(O({taskId:g.taskId},g.resultMap))):(g.resultMap.resultMap=Ge(g.resultMap.resultMap),E.multiAgent.tasks.push([O({taskId:g.taskId},g.resultMap)]))})(r,u,h,C);break;case"deep_search":(function(g,E,w,p){const k=g.resultMap.resultMap;w!==-1?p!==-1?function(M,N,B,b){const T=M.multiAgent.tasks[N][B];T.resultMap.isFinal=b==null?void 0:b.isFinal,T.resultMap.messageType=b==null?void 0:b.messageType,function(R,D){var U,L;(U=D==null?void 0:D.query)!=null&&U.length&&(R.searchResult.query=D.query),(L=D==null?void 0:D.docs)!=null&&L.length&&(R.searchResult.docs=D.docs)}(T.resultMap,b==null?void 0:b.searchResult),T.resultMap.answer+=(b==null?void 0:b.answer)||""}(E,w,p,k):function(M,N,B){const b=B.resultMap.resultMap;b.answer=(b==null?void 0:b.answer)||"",Ye(b),M.multiAgent.tasks[N].push(O({taskId:B.taskId},B.resultMap))}(E,w,g):function(M,N){const B=N.resultMap.resultMap;B.answer=(B==null?void 0:B.answer)||"",Ye(B),M.multiAgent.tasks.push([O({taskId:N.taskId},N.resultMap)])}(E,g)})(r,u,h,C);break;default:(function(g,E,w){w!==-1?E.multiAgent.tasks[w].push(O({taskId:g.taskId},g.resultMap)):E.multiAgent.tasks.push([O({taskId:g.taskId},g.resultMap)])})(r,u,h)}}(l,i,o)})(a,n)}return n};function Ve(a,n){return O({taskId:a},n)}function Ge(a){return K(O({},a),{codeOutput:a.codeOutput||a.data||"",fileInfo:a.fileInfo||[]})}function Ye(a){var n,l;a.searchResult?(a.searchResult.query=((n=a.searchResult)==null?void 0:n.query)||[],a.searchResult.docs=((l=a.searchResult)==null?void 0:l.docs)||[]):a.searchResult={query:[],docs:[]}}const qe=(a,n,l)=>{var C,g;const{plan:i,tasks:o,plan_thought:f}=l!=null?l:{},d=["tool_result","browser","code","html","file","knowledge","result","deep_search","task_summary","markdown","ppt"];let m;a.thought=f||"";let r=i;const u=[],h=(C=o==null?void 0:o.filter(E=>E&&(E==null?void 0:E.length)>0))!=null?C:[],I=n?Array.from({length:(h==null?void 0:h.length)||0},()=>[]):[[{hidden:!1,children:[]}]];return h==null||h.forEach((E,w)=>{E==null||E.forEach((p,k)=>{var R,D,U,L;const M=p.messageTime,N=M==null?void 0:M.concat(String(k)),B=(p==null?void 0:p.messageType)==="code"&&(((R=p.resultMap)==null?void 0:R.codeOutput)||!((D=p.resultMap)!=null&&D.code)),b=(p==null?void 0:p.messageType)==="deep_search"&&p.resultMap.messageType==="extend";let T=[];T=p.messageType==="deep_search"?function(_,z){const le=["extend","search"];return _.resultMap.messageType==="report"?[K(O({},_),{id:z})]:le.includes(_.resultMap.messageType)?_.resultMap.searchResult.query.map((be,re)=>{var J,V,W;const Q=structuredClone(K(O({},_),{id:z.concat(String(re))})),Be={query:be,docs:(W=(V=(J=_.resultMap.searchResult)==null?void 0:J.docs)==null?void 0:V[re])!=null?W:[]};return Q.resultMap.searchResult=Be,Q}):[K(O({},_),{id:z})]}(p,N):[K(O({},p),{id:N})],p.messageType==="task"?I[w].push(K(O({},p),{task:p.task,hidden:!1,children:[]})):(p==null?void 0:p.messageType)==="result"||B||((L=(U=I[w])==null?void 0:U.at(-1))==null||L.children.push(...T)),!d.includes(p==null?void 0:p.messageType)||B||b||u.push(...T),(p==null?void 0:p.messageType)==="plan"&&(r=p.plan),(p==null?void 0:p.messageType)==="result"&&(m=p)})}),a.tasks=I,a.plan=r,a.conclusion=m,a.planList=(g=r==null?void 0:r.stages)==null?void 0:g.reduce((E,w,p)=>{const k=E.find(M=>M.name===w);return k?k.list.push((r==null?void 0:r.steps[p])||""):E.push({name:w,list:[(r==null?void 0:r.steps[p])||""]}),E},[]),{currentChat:a,plan:r,taskList:u,chatList:I}};var X;(function(a){a.PLAN="plan",a.PLAN_THOUGHT="plan_thought",a.TOOL_RESULT="tool_result",a.BROWSER="browser",a.FILE="file",a.DEEP_SEARCH="deep_search",a.CODE="code",a.HTML="html"})(X||(X={})),X.PLAN,X.PLAN_THOUGHT,X.TOOL_RESULT,X.BROWSER,X.FILE,X.DEEP_SEARCH,X.CODE,X.HTML;const $e=a=>a==null?void 0:a.map(l=>{const{domainUrl:i,fileName:o,fileSize:f}=l,d=o==null?void 0:o.split(".").pop();return{name:o,url:i,type:d,size:f}});var Ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMvSURBVHgB7ZtNTxNBHMafXSpWAogtctCqK6E1JoaUk/FWjnoxXrhwAJJCiBGC0ZMe1A9gNEo8oIlw8O43wAMmviS0MUYwWCxJjZJIosRoxLbjTts1mL7MdLc7283OL9mwkD/TeX6dne5LB5BIJF5GgQkmL5EYUTCSB2Ig0CCAr5vfk2+XU8mWPeqt1dWBNBpEXQLGL5IoVDzWd6MQzJfMFlbebJR+U+9+WB+4jAag8hZOTJMRomIRDoQvJz/T17uc0LREFyzCJWByhmgkj3l9uFh+wcZBoj6VLFqVwCUglyu8802IdQlMAeNTZFTURGcOaxKYAhSC82h6zEtgCiBo5nd/N+Yk8MwBTTDr81K/BO6PQafxt7VyVhYlcBa7R0BXoKMuCX29iTs8la4RQDnZf6yOav1kqe91jFXlKgF0FJwZPIUDwXaueoWoI6waH1yGf18roqcjyGZzyP7J1azNExJbS9UscZ8AA5+vpbAx0FgFrjoE7EAKgE20tQGhw7BMI9qohW1zwOiwfgrZD7xfA27fgymuTgORsLU2WNg2Amh4yokwTBMJW2+DhZwD4HGkAHgcKQAex/MCHLsYCgaBseHi/vwT/dHXFhzBkRFAw18pneXRje53B+EIwgUY4bsDu/4WcE6CUAGVwm9vF29qOCVBmIBK4W/e+Ix4fMNRCUIEVAu//ikIf2fIUQlCBFQNr9/fK2xVJIhAiAAjPA0Yj6f/hTeoJkEEwuYAI/wODv0X3qCSBBEIEWCE93ceqRjewAkJtglYefer8JM3vEElCZnMDuzCtlPha9c3MRjbi6XnP7nDGxRrQxgaSuHc2Q68ePkb7UENdmCbgIMhDUuvfugd7+F5gFEGlXA0HCm2sb8HdmHrxVBXgO8Znt1t1ELeD4DHkQLgcaQAeBwpgFmhIA338o1VwPNV2STcC7PvTAH65cgC3IrC7jvXipGJKfKRNPU3xstR9EN37r5ynFXHNQmSHC7AZfD2mUvAwwdKUh8rY3ALel8LfeYqrQO6dCafw2KzHg502OcJxh7NKs+4/wcmoKtI6EIKQpfNOb2OSA9d+qRamJtVnkIikUjq4C+/owRahWHFqQAAAABJRU5ErkJggg==";const et={doc:Ae,docx:Ae,xlsx:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAOGSURBVHgB7ZtNaBNBGIbf2TRaW4WCNFqbho1UERSJoODJpoje2gas4q0/R0/14KkGC6VeerEXr+pNaA9FvXlIvXgoAUNBEIM1rfWv1WqqATHdHXcWUoptsrPJzm6W3QfabJpvoN+z8/NlkgF8fHy8DEEVXFlOJCQoA6CIUQIZNpD//Suz+OljRlL3TGUvv87AIkwJ6P+QiBNVeWBX0ttZz+ex9OWzfk2Be4uXsjdhAdwCrq30DqgqfQiH2C6AoUnIqA3F7lx37idqQOIJuv4+ITuZ/G5ody4mbQZTckpuQQ1wCVAkJYU6xAoJhgKuLvcMOjHmealVAk8P6EOdU4sEQwEUJA4XUK0Ejh5Aa5pk7KQaCVyTYD2wJxjkijMrwTUC9jc1mZVwhyfWNQIYR4+0IyDx/cuahJHO551xozhXCdjX2IgTchQHtN7AAyVkwCimAS6DDYPOjggUVYWiKJWDKY2/Q7ZiiOsElGBDgWM4yEYBrhoCIvAFwON4XoCwSTAUCOHGwRH9+v73KawqX80019vfah1FQS1gcm0CBVqACIT1gK7mizi595T+Mxaa0BI6xN2WJT8Wugs5GNXbn2s6D1EIEzD/Y37rurUhxC2hlDxrw9jY3EA6n4YohAnIqm+QzCa3nvNI2C354YVhrNNvEIUwAaxiWwikuSWUS77YUuB+E1QNQlcBXglOJc8QvgwaSXAyeYbh5wL9yz0UFvC3WMRp5SzGj41v/W1tc1V/FJn8TORpxRxtK4TK9QSn7nwJWyvBkoTJxckdryXfJm1PnmF7KRxubMdgZOc+xe3jo9prYdiNrQJ2m/BKmCmWrMQ2AeVme7PFktXYIqDSUmemWBKBcAFG67zZitFqhArgLXKclCBMgNkKr5KEZtIMUQjdDzBb5JST4Nr9AJa42Qrvfwkrf1aE7gcI2xJj+wFd8xf068jhNlMVni4BaZx5GdOfR9vDCAi6V8IElD7Bcao9L/62ODyOLwAexxcAj+MLgMcxFEAocnAvht8kN+4BBJYdTrAdihdGIcZflSV0Ci6FEMwaxoCD/qXeV9pYiMFFEJDcdORJ1CiOaxIkAWlI+13TyQy7CQSlbp44LgHT4dkMAbXkjI4daN166HHbbI4zlh92dEaR1JQ2L8ioRyjJsN7Kbhhvk6qOzbFTJNpDH6VEdnpu0JdpIs1Rojya6Xg2Bx8fHx8T/AO1kHvizwwJagAAAABJRU5ErkJggg==",csv:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAT/SURBVHgB7ZtNbBtFFMf/u3bWju2krk0+nIZkU5MgKhFFHJBAIIVKDeHjkMINVEHFBbhF4t5jD0XiRBAIiRTEtTQSSKFINFQ9oUpErRA0bRqTpPmkjfNhx3ZiT+d5aztpY+869o7txj/J2l37ZXfnP2/eezObBapUqXKQkbAP+m+xXsbwARh6mQQVAthYC4/PzCyMWyCfD5zsGEORyEuAExOsRwa+YEAvBLO6soG5u8vagcSGuRCDXIggCkQ2akiNlyRcLkXjH4NJH24zdln9acqNAjEkQP8UU6nx3OULvmCx4K7bUwwRDAmQ2Cqvxqcohgi6AlDA4xsVZUqhIugKkIz2ZU4hIugLwE+OCmC/IhiJAWU39rOxHxEMp8FSY69VDNnlK0LFCGCzK3A67YZsSYR4IvGdEduKEYBo9HkhWwzesiQNcC/o1TOrKAHs3AuOPnPEsCfEGTujZ2NFhVFTY0Vbhw+JRALxeCKnLU/hPTPITUV5wE5kWU6KkeujKFb3wBTLGQwrVgCjRCI42ALoITwGOLnkL7uAJn7lphogFOeLHXwoT0aBOzFgcQtCESYANfyUF+ir1/azcX0T+HxRnBBChgD19FftwEl37sYT3bXA96q2FYEQD/isSXP5FNMbUVxd2MB/69HkcbfXgec9DrS5MuUuCUDeYDamC0AN2dmbZ/+ax9CdddQfcvHy1ob4dhzDi+tYW53HC3UWvNXuRnudgpE1LobNAbMRIkCK6Y0YLkRrcazbt6dtKBLD18tBRGbW0dreIsQ9Tb8Gjf8UQVmBx5t9VkcTnuYjjRCJ6UEwFomm94/a9YOgaEy/nZvhTL3u4lc717rbK0qN6QKMzIWwGounj/02nhLbtMwgKtXlwvQYUHP4MN75PYALx1UcUizJ72gYnKjXPgSlu0tr2lZ0JWi6B1itFsSafTg+Ookfb93b04Y8gTyCCiCqFkWi+2ywb4JNoQjPBaI8xc1Oz8EV3cRLT9XibdWNV5rr0l6xE5oXfDqNoiDH0TH6nBTI9ruwuQClOH+Xmty/di+I324EsXp1Fi+6FbzX6cX7nZmupzhxygP8cB+mI8wDshEOhTF/dwnNW5v45c2utEfQDPHdSRSMngeUPCs7nI6kZ1j9fnxz8//095QyRdQMZVOW0BC5Le+u/V0WmE5Z1WVO524BRKREUwWgio8qv6E2LbDp8YYn0+V/rsQgAlMF8CtajqfGkwgfN+xdBtNYp99ShRHx83IUIjA1DY7NBnGmJbMoSytC9Ak9XAMkSJCmR+5i6O8lXOGr2TYDXlMopnoAq3XgkyuBXXMBgno8tVCyV+O/XNjmjTf2MLRQTPUAiuzXFHeyDP7I78arPldy6etRSKAb98M4d30JUzYXWttaIArTK0GP140oj+7frqzj7L+zCIU2caxeSRc8tEq0uA00NHnQ0PI0fIJ6PoWQUpg8ocHu5Y3MlLuhqBYEGi1WtFgFJPwslOzhqE1EhDPAgX80VhUAB5yqAHjCsduR8z/Kn3gBLnZIBQrA8AcqFL7cNaZnoyuALGMYFQoX4LwBG31en2Dl8aJEHvCGBX7tkjr07AzFACmO0/yMBb+eIhLGMGjEzpAAtKrKT/hapYggJTB46VnpoiFb5EH/P0xlluR7QyrKEHJ7ScLp0U5pLI+/yZ++22wA2mtzKkr8PoHEEGAyxmWGkdEuaRhVqlSpkgcPAOuPe9ZvTI3+AAAAAElFTkSuQmCC",pdf:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAScSURBVHgB7ZtNaBRJFMf/PT1MyMeIGo32mjWzuxoIeEhQ9KTEk952FQRxAskieBCEDQgKggviUXC9eBBlE4ziSVZPHgRdPMkqjifBmLUVFRwcjd+kzaR9r2YmGUXTNZmaqmnSPwjT3XlD8v7v1atXNVNARETEfMbCHPD7+n7D1FQ/LKubblPQwOtXrzJjo6OZGHCih16hiIoEIMd74ft/Q5PT5eRevMCjsTFxTSL81fPgwSAUIC2An07308sQDFEuAEP/eMaanNzc47rjqIKYjJE/MJCCQee/hQ90+/H4tTup1EJUgZQA+PTpGuoQFSIECuDv2jUAA2NelmpFCM6AWOxX1DnViBAsgO/3IgTMVQSZGlBVkdHJXESQK4J1QCKRkLKrVITQCJBcsACJhgYpWxYB8fifMrahEYD5ubMTdjwuZTsF/PHfqlW9QXahEqCpqQlda9aghbJBhphl9QfZyMlZR/Aw6OzqQn5yEvl8flZbn2ew0dFZbUInQAkeChLDIRVkEKohUAvMZAClsPh5/Bi4dQsm0S/Apk3Anj0z9yMjwJUrMIX+IbBxo3h5e/Nm4X77dpjEXA24eBH5N294bgOam2EK7QIIp4mk48D2PHHtvXwJU2gXwLt7t3Cxdi2wZIkQRLbFrQXaBfBdt3BRrAXe/fswiXYBGh4+nCmADE+FBtEugG3b8K5enXlw+zZMYmQWaCyLeoJWeCYxIkATF8Ai9o4dwNKlMIWZPqBYAAXcB5R3hprRLwBHm9cBRI6aIQHfG+oI9QuwbZt4eUczgXf6ND7cu1d4zgJ8LQJ3iNQr1BK9iyGO/rp14tK/cQPO4sV4dvgw4sePI9HeXhCgo6Ngx47z8CjBhfPsWaAkmCL0ZgCPfXLKe/IEFgnALKNNjeyxYzM2XCBXrvzSeYafDQ4qXzfoywCOKi+FiRwtgZ3164WzNj1rJ2e5Jf5I0W3ZsGH6LVwjPhYj7uzbB5v3AhsbgffvoQo9AnDUtmyZHs9te/cC5AzXgbdnzuADN0PkaDKZxDNqlH44ckQI0fpVTeDMwdOnStcOtRWg5PjWrdMpzZHODg9j/NIlJCcmsHDRIjgc2dWrxe9baaMze+AAnlPGJLq70VycMSbIcY/e07F8OVRSOwE43fv6vhjL45cvI3f0KJaRE853HOFW2eGCiOJMUawVSfpkqEWx84x6ATjq7Hh5s4NC+toUwV8qaH1l9/+rQf0skE4L5znVXUrlErlTp0TK1xtqBSir9OO04vvx0CFxnT15Eq2K529VKBWAo17a8uIKztNWdmhI7P+Z3PWZDaU1wKYU/3/3brTt3y/uueglqJC11aB4qUJ5EeT6nTt4UHx2x44n6th5RrkAnOrOihUIC/P+s8FIAMxzIgEwz5ERwEV4CfwmebAAlqXscIJ2fP/fIBOZDDiB8PJPkIHUgQk/nb4D/vJhuHCtc+d+CjKSK4K+/zskxlNdkc9vljGTEsA6fz5DIig5o6MFCph14YIrY1rZoamdO1O0Z8WnR1KoTzLCeQ6YJHM7NsenSAoHKVL0B03XBpd+rtNsNWyNjFxHRERERAV8BtplXGJ0GKhqAAAAAElFTkSuQmCC",txt:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALTSURBVHgB7ZtPaxNBHIbf2WTb7BZrQo0aLJpQCh7jraiV9aDgrX6AtuYj5KYn66V48wsITaniUb0o9JIU//WYW/WioUJIEGyNbZp23R0nsYoIZifZSTbbnQcS5vBusvMw+5uFmQEkEkmQIegCunLZAKHzrGmAIok+sPt9t1jeLBcJlOXJ+5UCBNGRAPr4Yho2eYBmx/tMbauGSrnaarObzqkRPZtaKG3DJQpv8LDzeXjQ+X+hwK2D/Xr+00IyCpdwCaBPppKHnXf9h8KgSIuQwDcCrNBgdf43AiQ4CmgVvD4Vuq5wKcF5BPyq9oONCwkcjwBJww90KYFDAB28Z/9/dCGBexr0mmFtmC/YoQT/CIgMQ9M1vnBLwt4ST9Q3AprEE3EoIc5bpnRm4/ZpwynmKwERNgrOTZyFrutc+RDoXadMGD5DVVWMp87Atm1Ylu2QpmwGq7ZN+GoE/I2iKExG2OGjRulTo20x9K0AbrYRcAEOSAEIOL2ZBYxFIDYJoexUgLU7wMEORCJewKkLwPgVCGckwX53Gvj4EiIR/giY1Q2YW58hGrtRQ2NzHaIRPgJUNPDl4TXsh2Ntc9r56xi7ca/Vrn9YxdcX7V/aiFVH4uRI8wUAIulJDYgnTjiHokN/mnrYgh4POVxwDL1AToMIOFIAAo4UgIAjBSDgSAEIOFIAAo4UgIAzEALsxjd4hWcrQ3vvV2EOTUDRjkMproBvsUs8ngnQVAvmu0WYbHkrNubdFgRP1wZHo6PwGlkEEXCkAAQcKQBHnSja7ig/8gLIzYJLARRr8C2k4JTg2Sqbg1+h9rJThOvECH10Kc9GggE/QVEic29STjG+GmBaGQCuj6f0FaJkeWJcAkhmvYQf9Cr8IoEgS2ZfPeOLdgBdmkoiFMqzq5IYRNiwByEZMvu6wHtJl8fmpmfY93xLBKXenicgrNOUFFnBe07m3uYgkUgkHfATOqbV6CDiho4AAAAASUVORK5CYII=",html:"data:image/png;base64,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"},tt=[{label:"\u5B9E\u65F6\u8DDF\u968F",value:H.follow,split:!1},{label:"\u6D4F\u89C8\u5668",value:H.browser},{label:"\u6587\u4EF6",value:H.file}],os=tt[0].value,ve=[{name:"\u7F51\u9875\u6A21\u5F0F",img:"icon-diannao",type:"html",placeholder:"GoData\u4F1A\u5B8C\u6210\u4F60\u7684\u4EFB\u52A1\u5E76\u4EE5HTML\u7F51\u9875\u65B9\u5F0F\u8F93\u51FA\u62A5\u544A",color:"text-[#29CC29]"},{name:"\u6587\u6863\u6A21\u5F0F",img:"icon-wendang",type:"docs",placeholder:"GoData\u4F1A\u5B8C\u6210\u4F60\u7684\u4EFB\u52A1\u5E76\u4EE5markdown\u683C\u5F0F\u8F93\u51FA\u6587\u6863",color:"text-[#4040FF]"},{name:"PPT\u6A21\u5F0F",img:"icon-ppt",type:"ppt",placeholder:"GoData\u4F1A\u5B8C\u6210\u4F60\u7684\u4EFB\u52A1\u5E76\u4EE5PPT\u65B9\u5F0F\u8F93\u51FA\u7ED3\u8BBA",color:"text-[#FF860D]"},{name:"\u8868\u683C\u6A21\u5F0F",img:"icon-biaoge",type:"table",placeholder:"GoData\u4F1A\u5B8C\u6210\u4F60\u7684\u4EFB\u52A1\u5E76\u4EE5\u8868\u683C\u683C\u5F0F\u8F93\u51FA\u7ED3\u8BBA",color:"text-[#FF3333]"}],st=ve[0],at=["task_summary","result"],je=a=>{const{files:n,preview:l,remove:i,review:o}=a,f=r=>{const u=["B","KB","MB","GB"];let h=0;for(;r>=1024&&h<u.length-1;)r/=1024,h++;return`${r==null?void 0:r.toFixed(2)} ${u[h]}`},d=r=>["jpg","png","jpeg"].includes(r.type)?r.url:et[r.type]||Ae,m=(r,u)=>(0,s.jsxs)("div",{className:"group w-[200px] h-[56px] rounded-xl border border-[#E9E9F0] p-[8px] box-border flex items-center relative "+(l?"cursor-pointer":"cursor-default"),onClick:()=>(h=>{o==null||o(h)})(r),children:[(0,s.jsx)("img",{src:d(r),alt:r.name,className:"w-32 h-32 shrink"}),(0,s.jsxs)("div",{className:"flex-1 ml-[4px] overflow-hidden",children:[(0,s.jsx)(q.Z,{title:r.name,children:(0,s.jsx)("div",{className:"w-full overflow-hidden whitespace-nowrap text-ellipsis text-[14px] text-[#27272A] leading-[20px]",children:r.name})}),(0,s.jsx)("div",{className:"w-full text-[12px] text-[#9E9FA3] leading-[18px]",children:f(r.size)})]}),l?null:(0,s.jsx)("i",{className:"font_family icon-jia-1 absolute top-[10px] right-[8px] cursor-pointer hidden group-hover:block",onClick:()=>(h=>{i==null||i(h)})(u)})]},u);return(0,s.jsx)("div",{className:"w-full flex gap-8 flex-wrap",children:n.map((r,u)=>m(r,u))})};var Ee={loadingDot:"index-module_loadingDot__giNYW",dot:"index-module_dot__hJq9k","dot-pulse":"index-module_dot-pulse__7NRpI",dot_0:"index-module_dot_0__oY0y5",dot_1:"index-module_dot_1__JJWJb",dot_2:"index-module_dot_2__5p5In"};const is=()=>{const a=new Array(3).fill(void 0);return(0,s.jsx)("div",{className:Ee.loadingDot,children:a.map((n,l)=>(0,s.jsx)("div",{className:P()(Ee[`dot_${l}`],Ee.dot)},l))})},ae=a=>{const{className:n,children:l,color:i="white"}=a;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:P()("relative size-[1em] shrink-0",n),children:(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-[#4040ff] to-transparent animate-spin bg-clip-padding p-2",children:(0,s.jsx)("div",{className:"absolute inset-2 rounded-full",style:{backgroundColor:i}})})}),l]})},nt=a=>{switch(a){case"plan":return(0,s.jsx)(Mt.Z,{});case"plan_thought":return(0,s.jsx)(It.Z,{});case"tool_result":return(0,s.jsx)(Tt.Z,{});case"browser":case"deep_search":return(0,s.jsx)(bt.Z,{});case"file":default:return(0,s.jsx)(Bt.Z,{});case"code":case"html":return(0,s.jsx)(Rt.Z,{})}},cs=({plan:a})=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-[16px] font-[600] mb-[8px]",children:"\u4EFB\u52A1\u8BA1\u5212"}),a.map((n,l)=>(0,s.jsxs)("div",{className:"mb-[8px]",children:[(0,s.jsxs)("div",{className:"h-[22px] text-[#2029459E] text-[15px] font-[500] flex items-center mb-[5px]",children:[(0,s.jsx)("div",{className:"w-[6px] h-[6px] rounded-[50%] bg-[#27272a] mx-8"}),n.name]}),(0,s.jsx)("div",{className:"ml-[22px] text-[15px]",children:n.list.map((i,o)=>(0,s.jsxs)("div",{className:"leading-[22px]",children:[o+1,".",i]},o))})]},l))]}),ds=({tool:a,changePlan:n,changeActiveChat:l,changeFile:i})=>{var f,d,m,r;const o=(u=>{const h="tool_result",I="code",C="html",g="plan_thought",E="plan",w="file",p="knowledge",k="deep_search",M="markdown",N="web_search",B="internal_search",b="code_interpreter";switch(u.messageType){case h:return function(T){var D,U,L;const R=(D=T==null?void 0:T.toolResult)==null?void 0:D.toolName;switch(R){case N:case B:return{action:"\u6B63\u5728\u641C\u7D22",tool:"\u7F51\u7EDC\u67E5\u8BE2",name:((L=(U=T==null?void 0:T.toolResult)==null?void 0:U.toolParam)==null?void 0:L.query)||""};case b:return{action:"\u6B63\u5728\u6267\u884C\u4EE3\u7801",tool:"\u7F16\u8F91\u5668",name:"\u6267\u884C\u4EE3\u7801"};default:return{action:"\u6B63\u5728\u8C03\u7528\u5DE5\u5177",tool:R||"",name:R||""}}}(u);case I:return{action:"\u6B63\u5728\u6267\u884C\u4EE3\u7801",tool:"\u7F16\u8F91\u5668",name:""};case C:return{action:"\u6B63\u5728\u751F\u6210web\u9875\u9762",tool:"\u7F16\u8F91\u5668",name:""};case g:return{action:"\u6B63\u5728\u601D\u8003\u4E0B\u4E00\u6B65\u8BA1\u5212",tool:"",name:""};case E:return{action:"\u66F4\u65B0\u4EFB\u52A1\u5217\u8868",tool:"",name:""};case w:return function(T){var D,U,L;const R=((U=(D=T.resultMap)==null?void 0:D.fileInfo)==null?void 0:U[0])||{};return{action:((L=T==null?void 0:T.resultMap)==null?void 0:L.command)||"",tool:"\u6587\u4EF6\u7F16\u8F91\u5668",name:(R==null?void 0:R.fileName)||""}}(u);case p:return{action:"\u6B63\u5728\u8C03\u7528\u77E5\u8BC6\u5E93",tool:"\u6587\u4EF6\u7F16\u8F91\u5668",name:"\u67E5\u8BE2\u77E5\u8BC6\u5E93"};case k:return function(T){var D,U,L;const R=T.resultMap.messageType==="report";return{action:R?"\u6B63\u5728\u603B\u7ED3":"\u6B63\u5728\u641C\u7D22",tool:"\u6DF1\u5EA6\u641C\u7D22",name:R?((D=T==null?void 0:T.resultMap)==null?void 0:D.query)||"":((L=(U=T==null?void 0:T.resultMap)==null?void 0:U.searchResult)==null?void 0:L.query)||""}}(u);case M:return{action:"\u6B63\u5728\u751F\u6210\u62A5\u544A",tool:"markdown",name:""};default:return{action:"\u6B63\u5728\u8C03\u7528\u5DE5\u5177",tool:(u==null?void 0:u.messageType)||"",name:""}}})(a);switch(a.messageType){case"plan":{const u=((f=a.plan)==null?void 0:f.stepStatus.lastIndexOf("completed"))||0;return(0,s.jsxs)("div",{className:"mt-[8px] flex items-center px-10 py-6 bg-[#F2F3F7] w-fit rounded-[16px] cursor-pointer overflow-hidden  max-w-full",onClick:()=>n==null?void 0:n(),children:["\u2714\uFE0F",(0,s.jsxs)("div",{className:"px-8 flex items-center overflow-hidden",children:[(0,s.jsx)("div",{className:"shrink-1",children:"\u5DF2\u5B8C\u6210"}),(0,s.jsx)("div",{className:"text-[#2029459E] text-[13px] flex-1 overflow-hidden whitespace-nowrap text-ellipsis ml-[8px]",children:(d=a.plan)==null?void 0:d.steps[u]})]})]})}case"tool_thought":return(0,s.jsxs)("div",{className:"rounded-[12px] bg-[#F2F3F7] px-12 py-8 mt-[8px]",children:[(0,s.jsxs)("div",{className:"mb-[4px]",children:[(0,s.jsx)("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"7505",width:"16",height:"16",children:(0,s.jsx)("path",{d:"M511.307093 129.410685c181.026014 0 327.794573 144.591664 327.794573 322.867916 0 124.312168-71.379245 232.297622-175.984783 286.20442v24.23228c0 6.874406-0.572867 13.691524-1.775888 20.222209a55.568112 55.568112 0 0 1 27.497622 48.063553v37.236364c0 29.216224-22.341818 53.16207-50.985175 55.682685l-2.692475 0.17186h-22.513679c0 54.823385-45.199217 99.335161-100.939188 99.335161-55.625399 0-100.824615-44.45449-100.824616-99.335161h-19.248336l-2.234181-0.114573h-3.723637a56.312839 56.312839 0 0 1-49.954014-55.739972v-37.236364c0-20.164923 10.712615-37.809231 26.695609-47.662546a105.923133 105.923133 0 0 1-1.947749-20.623216V738.998601c-105.178406-53.792224-177.015944-162.006825-177.015944-286.72 0-178.276252 146.768559-322.867916 327.851861-322.867916z m45.48565 794.566714H466.795316c0 23.945846 19.935776 43.537902 44.97007 43.537902a44.282629 44.282629 0 0 0 45.027357-43.423329v-0.114573z m75.733035-93.148196h-240.604196l-0.286434 37.236363h241.291637l0.057287-36.94993-0.458294-0.286433zM511.307093 185.379804c-150.377622 0-271.940028 119.671944-271.940028 266.956084 0 100.538182 57.229427 191.165762 146.653986 236.88056l30.304671 15.581986v57.85958a53.84951 53.84951 0 0 0 0.916587 9.738741l0.572867 2.577902h65.994294v-114.115133l-95.095944-117.265902-0.744727-0.973874-1.260308-2.062321-0.401007-0.572868-0.572867-1.203021-1.718601-4.411076-0.802014-4.181931-0.171861-4.926657 0.171861-2.119608 1.203021-5.213091 1.718601-3.952783 1.718601-2.978909 3.265343-3.895497 3.66635-3.208056 1.489454-0.973874 4.582937-2.234182 3.551777-1.145734 4.010069-0.630154 2.520616-0.114573h201.763804c2.176895 0 4.296503 0.286434 6.416112 0.744727l3.83821 1.203021 3.036196 1.374881 4.296503 2.921622 4.067357 4.124644 2.692475 4.01007 2.119609 4.69751 0.916587 3.551776 0.51558 5.327665-0.229146 3.437202-0.802014 4.124644-0.973875 2.749762-1.317594 2.864336-1.145734 1.890461-1.718602 2.520616-94.694937 116.463888v114.115133h66.108867l0.458294-2.062322a56.541986 56.541986 0 0 0 0.916588-10.197035v-58.317874l30.304671-15.581986c88.966266-45.829371 145.680112-136.285091 145.680112-236.536839 0-147.226853-121.505119-266.956084-271.882741-266.956084v0.114573z m42.621314 369.327441H469.602365l42.163021 51.844475 42.163021-51.844475zM155.95761 250.11379l27.039329 15.639273a29.789091 29.789091 0 1 1-29.789091 51.615329l-27.039329-15.581986a29.789091 29.789091 0 1 1 29.789091-51.558042v-0.114574z m753.205706 10.999049a29.789091 29.789091 0 0 1-10.999049 40.673567l-26.982042 15.581986a29.789091 29.789091 0 0 1-29.789091-51.558042l26.924756-15.581986a29.789091 29.789091 0 0 1 40.845426 10.884475zM291.784407 76.592336l15.639273 26.982042a29.789091 29.789091 0 1 1-51.615329 29.789091l-15.639272-26.924756a29.789091 29.789091 0 1 1 51.615328-29.846377z m481.552112-10.884476a29.789091 29.789091 0 0 1 10.884476 40.673567l-15.639273 26.982042a29.789091 29.789091 0 0 1-51.558042-29.789091l15.581986-26.982042a29.789091 29.789091 0 0 1 40.673566-10.884476zM512.22368 0a29.789091 29.789091 0 0 1 29.789091 29.789091v31.163972a29.789091 29.789091 0 1 1-59.578182 0V29.846378a29.789091 29.789091 0 0 1 29.789091-29.789091V0z",fill:"#FF964B","p-id":"7506"})}),(0,s.jsx)("span",{className:"ml-[4px]",children:"\u601D\u8003\u8FC7\u7A0B"})]}),(0,s.jsx)("div",{className:"text-[#2029459E] text-[13px] leading-[20px]",children:a.toolThought})]});case"browser":return(0,s.jsx)("div",{className:"mt-[8px]",children:(m=a.resultMap)==null?void 0:m.steps.filter(u=>u.status!=="completed").map((u,h)=>(0,s.jsxs)("div",{children:[nt(a.messageType),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:o.action}),(0,s.jsx)("div",{children:u.goal})]})]},h))});case"task_summary":return(0,s.jsxs)("div",{className:"mt-[8px]",children:[(0,s.jsx)("div",{className:"mb-[8px]",children:a.resultMap.taskSummary}),(0,s.jsx)(je,{files:$e(a.resultMap.fileList),preview:!0,review:i})]});default:{const u=["html","markdown"],h=!((r=a.resultMap)!=null&&r.isFinal)&&(a.messageType==="deep_search"&&(a.resultMap.messageType==="extend"||a.resultMap.messageType==="report")||u.includes(a.messageType));return(0,s.jsxs)("div",{className:"mt-[8px] flex items-center px-10 py-6 bg-[#F2F3F7] w-fit rounded-[16px] cursor-pointer overflow-hidden max-w-full",onClick:()=>l(a),children:[h?(0,s.jsx)(ae,{color:"#F2F3F7"}):nt(a.messageType==="deep_search"&&a.resultMap.messageType==="report"?"file":a.messageType),(0,s.jsxs)("div",{className:"px-8 flex items-center overflow-hidden",children:[(0,s.jsx)("div",{className:"shrink-0",children:o.action}),(0,s.jsx)("div",{className:"text-[#2029459E] text-[13px] overflow-hidden whitespace-nowrap text-ellipsis flex-1 ml-[8px]",children:o.name})]})]})}}},ms=({tasks:a,isReactType:n,changeActiveChat:l,changePlan:i,changeFile:o})=>(0,s.jsx)(s.Fragment,{children:a.map((f,d)=>(0,s.jsxs)("div",{className:"overflow-hidden",children:[n?null:(0,s.jsx)("div",{className:"font-[500]",children:f.task}),(f.children||[]).map((m,r)=>(0,s.jsx)("div",{children:(0,s.jsx)(ds,{tool:m,changePlan:i,changeActiveChat:l,changeFile:o})},r))]},d))}),us=({chat:a,isReactType:n,changeActiveChat:l,changePlan:i,changeFile:o})=>(0,s.jsx)(s.Fragment,{children:a.tasks.map((f,d)=>{const m=d===a.tasks.length-1;return(0,s.jsxs)("div",{className:"w-full flex",children:[n?null:(0,s.jsxs)("div",{className:"w-[30px] mt-[2px] mb-[8px] relative shrink-0 overflow-hidden",children:[m&&a.loading?(0,s.jsx)(ae,{}):(0,s.jsx)("i",{className:"font_family icon-yiwanchengtianchong text-[#4040ff] text-[16px] absolute top-[-4px] left-0"}),(0,s.jsx)("div",{className:"h-full w-[1px] border-dashed border-l-[1px] border-[#e0e0e9] ml-[7px] "})]}),(0,s.jsx)("div",{className:"flex-1 mb-[8px] overflow-hidden",children:(0,s.jsx)(ms,{tasks:f,isReactType:n,changeActiveChat:l,changePlan:i,changeFile:o})})]},d)})}),xs=({chat:a,changeFile:n})=>{var i,o,f,d;const l=((o=(i=a.conclusion)==null?void 0:i.resultMap)==null?void 0:o.taskSummary)||((f=a.conclusion)==null?void 0:f.result)||"\u4EFB\u52A1\u5DF2\u5B8C\u6210";return(0,s.jsxs)("div",{className:"mb-[8px]",children:[(0,s.jsx)("div",{className:"mb-[8px]",children:l}),(0,s.jsx)(je,{files:$e(((d=a.conclusion)==null?void 0:d.resultMap.fileList)||[]),preview:!0,review:n})]})},ps=a=>{var m;const{chat:n,deepThink:l,changeTask:i,changeFile:o,changePlan:f}=a,d=!l;return(0,s.jsxs)("div",{className:"h-full text-[14px] font-normal flex flex-col text-[#27272a]",children:[(n.files||[]).length?(0,s.jsx)("div",{className:"w-full mt-[24px] justify-end",children:(0,s.jsx)(je,{files:n.files,preview:!1})}):null,n.query?(0,s.jsx)("div",{className:"w-full mt-[24px] flex justify-end",children:(0,s.jsx)("div",{className:"max-w-[80%] bg-[#4040FFB2] text-[#fff] px-12 py-8 rounded-[12px] rounded-tr-[12px] rounded-br-[4px] rounded-bl-[12px] ",children:n.query})}):null,n.tip?(0,s.jsx)("div",{className:"w-full rounded-[12px] mt-[24px]",children:n.tip}):null,!d&&n.thought?(0,s.jsx)("div",{className:"w-full px-12 py-8 bg-[#F2F3F7] rounded-[12px] mt-[24px]",children:(0,s.jsx)("div",{children:n.thought})}):null,!d&&((m=n.planList)!=null&&m.length)?(0,s.jsx)("div",{className:"w-full px-12 py-8 rounded-[12px] mt-[24px] bg-[#F2F3F7]",children:(0,s.jsx)(cs,{plan:n.planList})}):null,n.tasks.length?(0,s.jsx)("div",{className:"w-full mt-[24px]",children:(0,s.jsx)(us,{chat:n,isReactType:d,changeActiveChat:r=>{i==null||i(r)},changePlan:f,changeFile:o})}):null,n.conclusion?(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(xs,{chat:n,changeFile:o})}):null,n.loading?(0,s.jsx)(is,{}):null]})},hs=a=>{const{children:n,onClose:l}=a;return(0,s.jsxs)("div",{className:"text-[16px] font-semibold flex items-center justify-between mb-[8px]",children:[n,(0,s.jsx)(Pt.Z,{className:"cursor-pointer text-[16px]",onClick:l})]})},fs=a=>{const{value:n,onChange:l,className:i,options:o}=a,f=(0,A.useRef)(null),d=(0,A.useRef)(null),m=(0,G.Z)(()=>{if(!f.current)return;const r=f.current.querySelector(`[item-key="${n}"]`);if(!r)return;const{width:u}=r.getBoundingClientRect(),h=r.offsetLeft;d.current&&(d.current.style.width=`${u}px`,d.current.style.transform=`translateX(${h}px)`)});return(0,A.useEffect)(()=>{m();const r=new ResizeObserver(m);return f.current&&r.observe(f.current),()=>{r.disconnect()}},[m,n]),(0,s.jsxs)("div",{className:P()(i,"flex items-center relative box-border gap-4"),ref:f,children:[o.map(r=>(0,s.jsxs)(A.Fragment,{children:[(0,s.jsx)("div",{className:P()("pl-16 pr-16 h-32 rounded-[16px] cursor-pointer flex items-center relative z-10","sed-item"),"item-key":r.value,onClick:()=>l==null?void 0:l(r.value),children:(0,s.jsx)("span",{className:"text-inherit",children:r.label})},r.value),r.split&&(0,s.jsx)("div",{className:"m-[8px] mt-0 mb-0 bg-[#dcdfe6] w-1 h-[1em]"})]},r.value)),(0,s.jsx)("div",{ref:d,className:"h-full rounded-[16px] bg-[#f4f4f9] absolute left-0 top-0 transition-all -z-1"})]})},lt=(0,A.createContext)(Object.freeze({__proto__:null,InputSize:{big:"106",medium:"72",small:"32"},RESULT_TYPES:at,actionViewOptions:tt,defaultActiveActionView:os,defaultProduct:st,demoList:[{title:"Browser\u4EE3\u7801\u67B6\u6784\u5206\u6790",description:"\u5E2E\u6211\u5206\u6790github\u4E2D\u5F00\u6E90\u7684browser-use\u7684\u4EE3\u7801\uFF0C\u5E76\u8FDB\u884C\u5206\u6790",tag:"\u4E13\u4E1A\u7814\u7A76",url:"//storage.360buyimg.com/pubfree-bucket/ei-data-resource/89ab083/static/demoPage.html"},{title:"\u4EAC\u4E1C\u8D22\u62A5\u5206\u6790",description:"\u5206\u6790\u4E00\u4E0B\u4EAC\u4E1C\u7684\u6700\u65B0\u8D22\u52A1\u62A5\u544A\uFF0C\u603B\u7ED3\u51FA\u6838\u5FC3\u6570\u636E\u4EE5\u53CA\u516C\u53F8\u53D1\u5C55\u60C5\u51B5",tag:"\u6570\u636E\u5206\u6790",url:"//storage.360buyimg.com/pubfree-bucket/ei-data-resource/89ab083/static/demoPage2.html"},{title:"HR\u667A\u80FD\u62DB\u8058\u4EA7\u54C1\u7ADE\u54C1\u5206\u6790",description:"\u5206\u6790\u4E00\u4E0BHR\u667A\u80FD\u62DB\u8058\u9886\u57DF\u7684\u4F18\u79C0\u4EA7\u54C1\uFF0C\u5F62\u6210\u4E00\u4E2A\u7ADE\u54C1\u5BF9\u6BD4\u62A5\u544A",tag:"\u7ADE\u54C1\u8C03\u7814",url:"//storage.360buyimg.com/pubfree-bucket/ei-data-resource/89ab083/static/demoPage3.html"},{title:"\u8D85\u5E02\u9500\u552E\u6570\u636E\u5206\u6790",description:"\u5E2E\u6211\u5206\u6790\u4E00\u4E0B\u56FD\u5185\u9500\u552E\u6570\u636E",tag:"\u6570\u636E\u5206\u6790",url:"//storage.360buyimg.com/pubfree-bucket/ei-data-resource/89ab083/static/demoPage4.html"}],iconType:et,productList:ve}));lt.Provider;const we=a=>{var o,f,d;if(!a)return[];const{messageType:n,resultMap:l}=a,i=(o=a.toolResult)==null?void 0:o.toolName;if(n==="tool_result"){if(i==="internal_search"||i==="web_search"){const m=(f=a.toolResult)==null?void 0:f.toolResult,r=JSON.parse(m||"{}"),u=(r==null?void 0:r.data)||r||[];return Ke(m)&&u?u==null?void 0:u.map(h=>({name:h.pageName||h.name,pageContent:h.pageContent||h.page_content,url:h.sourceUrl||h.source_url})):[]}return[]}return n==="knowledge"?((l==null?void 0:l.refList)||[]).map(m=>({name:m.name,pageContent:m.pageContent,url:m.sourceUrl})):n==="deep_search"&&l.messageType==="search"?(((d=l==null?void 0:l.searchResult)==null?void 0:d.docs)||[]).map(m=>{const r=Array.isArray(m)?m[0]:m;return{name:r.title,pageContent:r.content,url:r.link}}):[]},rt=a=>{const n=(0,A.useMemo)(()=>we(a),[a]);return(0,A.useMemo)(()=>{var r;if(!a)return;const[l]=((r=a.resultMap)==null?void 0:r.fileInfo)||[],{messageType:i,toolResult:o,resultMap:f}=a,{fileName:d}=l||{};let m=!1;return i==="code"&&f.codeOutput?m=Ze(f.codeOutput):i==="tool_result"&&(o==null?void 0:o.toolName)==="code_interpreter"&&o.toolResult&&(m=Ze(o.toolResult)),{useBrowser:i==="browser",useCode:i==="code",useHtml:i==="html",useExcel:i==="file"&&(d.includes(".csv")||d.includes(".xlsx")),useFile:a.messageType==="file"&&!(d.includes(".csv")||d.includes(".xlsx")),useJSON:i==="tool_result"&&(o==null?void 0:o.toolResult)&&Ke(o.toolResult),isHtml:m,searchList:n,usePpt:i==="ppt"}},[n,a])},ce=A.memo(({loading:a,className:n,children:l})=>a?(0,s.jsx)("div",{className:P()("flex flex-col items-center justify-center w-full",n),children:(0,s.jsx)(ae,{className:"text-[32px]",children:(0,s.jsx)("p",{className:"mt-6 text-lg text-gray-700",children:l||"\u52A0\u8F7D\u4E2D"})})}):null);ce.displayName="Loading";const ot=(0,A.createContext)(null),gs=()=>(0,A.useContext)(ot);var As=ot.Provider;const vs=a=>{const{children:n}=a,l=(0,A.useRef)(null);return(0,A.useEffect)(()=>{l.current&&Jt.Z.contentLoaded()},[n]),(0,s.jsx)("div",{className:"mermaid",ref:l,children:n})},js=o=>{var f=o,{inline:a,className:n,children:l}=f,i=jt(f,["inline","className","children"]);const d=/language-(\w+)/.exec(n||""),m=(0,G.Z)(()=>{var r;We(l),(r=se())==null||r.success("\u590D\u5236\u6210\u529F")});return d&&d[1]==="mermaid"?(0,s.jsx)(vs,{children:l}):!a&&d?(0,s.jsxs)("div",{className:"rounded-[8px] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex justify-start items-center px-12 py-8 bg-[#f0f0f0] text-[12px] text-[#666] border-b-1 border-b-[#ddd]",children:[(0,s.jsx)("div",{className:"lang",children:d[1]}),(0,s.jsxs)("div",{className:"flex items-center cursor-pointer ml-auto",onClick:m,children:[(0,s.jsx)(_t.Z,{className:"flex items-center cursor-pointer"})," ",(0,s.jsx)("span",{style:{marginLeft:4},children:"\u590D\u5236"})]})]}),(0,s.jsx)(Qt.Z,K(O({},i),{children:l,language:d[1],PreTag:"div",wrapLongLines:!0,wrapLines:!1,customStyle:{padding:16}}))]}):(0,s.jsx)("code",K(O({},i),{className:n,children:l}))},ke=a=>{const{markDownContent:n,className:l}=a,{scrollToBottom:i}=gs()||{};return(0,A.useEffect)(()=>{n&&(i==null||i())},[n,i]),n?(0,s.jsx)("div",{className:P()("w-full markdown-body",l),children:(0,s.jsx)(Kt.UG,{remarkPlugins:[Zt.Z],components:{code:js},children:n})}):(0,s.jsx)(Z.Z,{description:"\u6682\u65E0\u5185\u5BB9",className:"mx-auto mt-32"})},Ne=(0,A.memo)(a=>{const{className:n,children:l,onClick:i,title:o}=a;return(0,s.jsx)("div",{className:P()("cursor-pointer flex w-24 h-24 items-center justify-center rounded-[4px] hover:bg-[#eee]",n),onClick:i,title:o,children:l})});Ne.displayName="ToolItem";const de=(0,A.memo)(a=>{const{htmlUrl:n,className:l,downloadUrl:i,showToolBar:o,outputCode:f}=a,[d,{setTrue:m,setFalse:r}]=(0,fe.Z)(!1),[u,h]=(0,A.useState)(null);(0,A.useLayoutEffect)(()=>{n&&m()},[n,m]);const I=(0,A.useMemo)(()=>o&&(0,s.jsxs)("div",{className:"absolute bottom-8 right-0 py-0 px-16 bg-[#fbfbff] h-[36px] rounded-[18px] flex items-center border-[#52649113] border-solid border-1 gap-12 text-primary",children:[(0,s.jsx)(Ne,{onClick:()=>ie(n),title:"\u5728\u65B0\u7A97\u53E3\u6253\u5F00",children:(0,s.jsx)("i",{className:"font_family icon-zhengyan"})}),(0,s.jsx)(Ne,{onClick:()=>ie(i),title:"\u4E0B\u8F7D",children:(0,s.jsx)("i",{className:"font_family icon-xiazai"})})]}),[o,n,i]),C=(0,A.useMemo)(()=>u?(0,s.jsx)("div",{className:"text-red-500",children:u}):n?(0,s.jsx)("iframe",{className:"w-full h-full",src:n,onLoad:r,onError:()=>{h("\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5 URL \u662F\u5426\u6B63\u786E"),r()}}):(0,s.jsx)(Z.Z,{description:"\u6682\u65E0\u5185\u5BB9",className:"mt-32"}),[u,n,r]);return!n&&f?(0,s.jsx)(ke,{markDownContent:f}):(0,s.jsxs)("div",{className:P()(l,"relative"),children:[(0,s.jsx)(ce,{loading:!!n&&d,className:"absolute left-0 top-0 w-full h-full"}),C,I]})});de.displayName="HTMLRenderer";const it=a=>{var C;const{fileUrl:n,mode:l,fileName:i}=a,o=(C=(i||n).split(".").pop())==null?void 0:C.toLowerCase(),f=l||(o==="xlsx"||o==="xlsm"||o==="xlsb"||o==="xls"?"excel":"csv"),{data:d,loading:m,error:r}=(0,Fe.Z)(()=>xe(this,null,function*(){const g=yield fetch(n);if(!g.ok)throw new Error("\u7F51\u7EDC\u9519\u8BEF");return f==="excel"?g.arrayBuffer():g.text()}),{refreshDeps:[n]}),{columns:u,dataSource:h,parseError:I}=(0,A.useMemo)(()=>{if(!d)return{columns:[],dataSource:[],parseError:!1};if(typeof d=="string"){const g=ts(d);return{columns:Object.keys(g[0]).map(E=>({title:E,dataIndex:E,key:E})),dataSource:g.map((E,w)=>K(O({},E),{key:w})),parseError:!1}}{const g=Se.ij(d,{type:"array"}),E=g.SheetNames[0],w=g.Sheets[E],p=Se.P6.sheet_to_json(w,{header:1,defval:""});if(!p.length)return{columns:[],dataSource:[],parseError:!1};const[k,...M]=p,N=k;return!N||!N.length?{columns:[],dataSource:[],parseError:!1}:{columns:N.map(B=>({title:String(B),dataIndex:String(B),key:String(B)})),dataSource:M.map((B,b)=>{const T={};return N.forEach((R,D)=>{var U;T[String(R)]=(U=B[D])!=null?U:""}),T.key=b,T}),parseError:!1}}},[d]);return m?(0,s.jsx)(ce,{className:"mr-32"}):r?(0,s.jsx)($.Z,{type:"error",message:"\u52A0\u8F7D\u5931\u8D25",description:r.message,showIcon:!0,className:"m-24"}):I?(0,s.jsx)($.Z,{type:"error",message:"\u89E3\u6790\u5931\u8D25",description:"\u6587\u4EF6\u683C\u5F0F\u6709\u8BEF\uFF0C\u65E0\u6CD5\u89E3\u6790\u3002",showIcon:!0,className:"m-24"}):u.length&&h.length?(0,s.jsx)(Et.Z,{columns:u,dataSource:h,scroll:{x:!0},bordered:!0,pagination:!1}):(0,s.jsx)("div",{className:"p-32",children:(0,s.jsx)(Z.Z,{description:"\u6682\u65E0\u6570\u636E"})})},ye=A.memo(a=>{const{fileUrl:n,fileName:l,className:i}=a,o=(0,A.useMemo)(()=>(u=>{var h;return(h=u==null?void 0:u.split(".").pop())==null?void 0:h.toLowerCase()})(l),[l]),{data:f,loading:d,error:m}=(0,Fe.Z)(()=>xe(this,null,function*(){const u=yield fetch(n);if(!u.ok)throw new Error("Network response was not ok");return yield u.text()}),{refreshDeps:[n]}),r=(0,A.useMemo)(()=>((u,h)=>u==="md"||u==="txt"?h||"":`\`\`\`${u}
${h||""}
\`\`\``)(o,f),[o,f]);return d?(0,s.jsx)(ce,{className:"mr-32"}):m?(0,s.jsx)($.Z,{type:"error",message:"\u52A0\u8F7D\u5931\u8D25",description:m.message,showIcon:!0,className:"m-24"}):(0,s.jsx)(ke,{markDownContent:r,className:i})});ye.displayName="FileRenderer";const ct=A.memo(({name:a,pageContent:n,url:l})=>(0,s.jsxs)("div",{className:"py-8",style:{borderBottom:"1px solid #52649a13"},children:[(0,s.jsxs)("div",{className:"flex gap-4 text-[#404040] cursor-pointer hover:text-primary",onClick:()=>ie(l),children:[(0,s.jsx)(Le.Z,{}),(0,s.jsx)("span",{children:a})]}),(0,s.jsx)("div",{className:"line-clamp-2 text-[#92909c] text-[12px]",children:n})]}));ct.displayName="SearchListItemComponent";const dt=A.memo(({list:a})=>(0,s.jsx)("div",{className:"w-fit flex flex-col",children:a==null?void 0:a.map(n=>(0,s.jsx)(ct,O({},n),n.name+n.url))}));dt.displayName="SearchListRenderer";const Ce=A.memo(a=>{const{taskItem:n,className:l,allowShowToolBar:i}=a,o=rt(n),{markDownContent:f}=(w=>{let p="";if(!w)return{markDownContent:p};const{messageType:k,toolResult:M,resultMap:N}=w;switch(k){case"tool_result":p=(M==null?void 0:M.toolResult)||"";break;case"code":(N!=null&&N.code||N!=null&&N.codeOutput&&(N!=null&&N.isFinal))&&(p=`\`\`\`python
${(N==null?void 0:N.code)||(N==null?void 0:N.codeOutput)}
\`\`\``);break;case"markdown":case"html":p=(N==null?void 0:N.codeOutput)||"";break;case"deep_search":case"report":p=N.answer||""}return{markDownContent:p}})(n),{resultMap:d,toolResult:m}=n||{},[r]=(d==null?void 0:d.fileInfo)||[],u=r==null?void 0:r.domainUrl,h=r==null?void 0:r.ossUrl,{codeOutput:I}=d||{},C=(0,A.useMemo)(()=>(()=>{if(!n)return null;const{useHtml:w,useCode:p,useFile:k,isHtml:M,useExcel:N,useJSON:B,searchList:b,usePpt:T}=o||{};return b!=null&&b.length?(0,s.jsx)(dt,{list:b}):w||T?(0,s.jsx)(de,{htmlUrl:u,className:"h-full",downloadUrl:h,outputCode:I,showToolBar:i&&(d==null?void 0:d.isFinal)}):p&&M?(0,s.jsx)(de,{htmlUrl:`data:text/html;charset=utf-8,${encodeURIComponent((m==null?void 0:m.toolResult)||"")}`}):N?(0,s.jsx)(it,{fileUrl:r==null?void 0:r.domainUrl,fileName:r==null?void 0:r.fileName}):k?(0,s.jsx)(ye,{fileUrl:r==null?void 0:r.domainUrl,fileName:r==null?void 0:r.fileName}):B?(0,s.jsx)(Wt(),{data:JSON.parse((m==null?void 0:m.toolResult)||"{}"),style:{backgroundColor:"#F2F3F7"}}):(0,s.jsx)(ke,{markDownContent:f})})(),[n,o,f,u,h,i,d==null?void 0:d.isFinal,m==null?void 0:m.toolResult,r,I]),g=(0,A.useRef)(null),E=(0,G.Z)(()=>{setTimeout(()=>{var w;(w=g.current)==null||w.scrollTo({top:g.current.scrollHeight,behavior:"smooth"})},100)});return(0,s.jsx)(As,{value:{wrapRef:g,scrollToBottom:E},children:(0,s.jsx)("div",{className:P()("w-full px-16",l),ref:g,children:C})})});Ce.displayName="ActionPanel";const Me=a=>{const{children:n,className:l,titleNode:i,footer:o,onClickTitle:f}=a;return(0,s.jsxs)(s.Fragment,{children:[i&&(0,s.jsxs)("div",{className:P()("py-8 flex items-center"),children:[(0,s.jsx)(Dt.Z,{className:P()("mr-4",{"cursor-pointer":f}),onClick:f}),i]}),(0,s.jsx)("div",{className:P()("w-full rounded-[12px] flex flex-col box-border flex-1 h-0 overflow-y-auto",l),style:{border:"1px solid #e9e9f0"},children:n}),o]})},mt="flex items-center justify-center rounded-[4px] size-16 font_family icon-fanhui cursor-pointer hover:bg-gray-300",Es=a=>{const{taskItem:n}=a,[l,{setFalse:i,setTrue:o}]=(0,fe.Z)(!1),f=(0,A.useMemo)(()=>{var h;if(!n)return"";const{messageType:r,resultMap:u}=n;if(r==="tool_result")return(h=n.toolResult)==null?void 0:h.toolName;if(r==="file"||r==="html"){const[I]=(u==null?void 0:u.fileInfo)||[];return(I==null?void 0:I.fileName)||r}return r==="deep_search"&&u.messageType==="report"?u==null?void 0:u.query:r},[n]),{useHtml:d,useExcel:m}=rt(n)||{};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:P()("h-34 w-full pl-[16px] pr-[16px] flex items-center justify-center text-[12px] font-semibold",d||m?"hover:text-primary cursor-pointer":""),style:{borderBottom:"1px solid #e9e9f0"},onClick:d||m?o:void 0,children:f}),(0,s.jsxs)(_e.Z,{destroyOnHidden:!0,open:l,onCancel:i,footer:null,styles:{body:{height:"100vh",boxSizing:"border-box"},content:{borderRadius:0,position:"absolute",width:"100%",height:"100%",top:0,left:0,padding:0}},className:"top-0 m-0 h-[100vh] overflow-hidden max-w-[100vw] rounded-none",width:"100vw",children:[(0,s.jsx)(Ce,{className:"flex-1 h-full",taskItem:n,noPadding:!0}),";"]})]})},ws=a=>{const{taskItem:n,className:l,taskList:i}=a,o=(0,A.useMemo)(()=>i==null?void 0:i.filter(g=>!["task_summary","result"].includes(g.messageType)),[i]),[f,d]=(0,A.useState)();let m=typeof f=="number"&&(o==null?void 0:o[f])||n;m||(m=o==null?void 0:o[o.length-1]),(0,A.useEffect)(()=>{n&&d(void 0)},[n]);const r=(0,A.useMemo)(()=>(o==null?void 0:o.findIndex(E=>E.id===(m==null?void 0:m.id)))||0,[m==null?void 0:m.id,o]),u=(o==null?void 0:o.length)||0,h=(0,G.Z)(()=>{d(Math.min(u-1,r+1))}),I=(0,G.Z)(()=>{d(Math.max(0,r-1))}),C=u-1;return(0,s.jsxs)(Me,{className:P()("h-full",l),children:[(0,s.jsx)(Es,{taskItem:m}),(0,s.jsx)(Ce,{className:"flex-1 h-0 my-8 overflow-y-auto",taskItem:m,allowShowToolBar:!0}),!!u&&(0,s.jsxs)("div",{className:"w-full flex items-center h-[38px] px-16",style:{borderTop:"1px solid #e9e9f0"},children:[(0,s.jsx)("i",{className:mt,onClick:I}),(0,s.jsx)("i",{className:P()(mt,"rotate-180"),onClick:h}),(0,s.jsx)(wt.Z,{className:"flex-1 text-primary",styles:{track:{background:"#4040FFB2"}},step:1,onChange:d,value:C?r:1,min:0,max:C||1,tooltip:{formatter:()=>{const{messageTime:g}=(o==null?void 0:o[r])||{};return Xt()(+g).format("YYYY-MM-DD HH:mm")}}})]})]})},ks=a=>{const{item:n}=a,{result:l}=n;return l!=null&&l.length?l.map(i=>(0,s.jsxs)("div",{className:"flex items-center w-full pb-16",children:[(0,s.jsx)(Le.Z,{className:"text-primary mr-6"}),(0,s.jsx)("div",{className:"flex-1 flex items-center w-0",children:(0,s.jsx)("span",{className:"hover:text-primary cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden",onClick:()=>ie(i.url),children:i.name})})]},i.name)):(0,s.jsx)(kt.ZP,{title:"\u6682\u65E0\u6570\u636E",status:"404"})},Ns=a=>{const{taskList:n}=a,[l,i]=(0,A.useState)(),o=()=>{i(void 0)},f=(0,A.useMemo)(()=>(n||[]).reduce((h,I)=>{const{toolResult:C,resultMap:g,id:E}=I,{toolParam:w}=C||{},{searchResult:p}=g||{},k=ze(I.messageTime),M=we(I);return M!=null&&M.length&&h.push({messageTime:k,query:(w==null?void 0:w.query)||(w==null?void 0:w.query)||(p==null?void 0:p.query)+"",id:E,result:we(I)||[]}),h},[]),[n]),d=(0,A.useMemo)(()=>(0,te.keyBy)(f,"id"),[f]),m=l&&d[l],r=(h,I,C)=>{var g;return(0,s.jsxs)("div",{className:"flex-1 flex items-center w-0",children:[(0,s.jsxs)("span",{className:P()("cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden",{"hover:font-medium":!I}),onClick:C||(()=>i(h.id)),children:["\u641C\u7D22\u201C",h.query,"\u201D"]}),(0,s.jsxs)("span",{className:"ml-8 text-[12px] whitespace-nowrap",children:["\u5171",((g=h.result)==null?void 0:g.length)||0,"\u4E2A\u7ED3\u679C"]})]})};let u=f.map(h=>(0,s.jsxs)("div",{className:"w-full pb-16 flex gap-8 items-center text-[#303133]",children:[(0,s.jsx)("span",{className:"font_family icon-sousuo mr-2"}),r(h),(0,s.jsx)("div",{className:"text-[12px] text-[#8d8da5]",children:h.messageTime})]},h.id));return f!=null&&f.length||(u=(0,s.jsx)(Z.Z,{})),m&&(u=(0,s.jsx)(ks,{item:m})),(0,s.jsx)(Me,{className:"p-16 overflow-y-auto",titleNode:m&&r(m,!0,o),onClickTitle:o,children:u})},ys=["file","code","html","markdown","result"],Cs=a=>{const{taskList:n,clearActiveFile:l,activeFile:i}=a,[o,f]=(0,A.useState)(),[d,{setFalse:m,setTrue:r}]=(0,fe.Z)(!1),u=(0,G.Z)(()=>{l==null||l(),f(void 0)}),{list:h,map:I}=(0,A.useMemo)(()=>{let p={};return{list:(n||[]).reduce((k,M)=>{var B,b;const{resultMap:N}=M;if(ys.includes(M.messageType)){const T=((b=(B=N==null?void 0:N.fileInfo)!=null?B:N.fileList)!=null?b:[]).map(R=>{var U,L;const D=(L=(U=R.fileName)==null?void 0:U.split("."))==null?void 0:L.pop();return K(O({},R),{name:R.fileName,url:R.domainUrl,task:M,messageTime:ze(M.messageTime),type:D})});k.push(...T.filter(R=>!p[R.name])),p=(0,te.keyBy)(k,"fileName")}return k},[]),map:p}},[n]),C=i||(o?I[o]:void 0),g=(p,k,M)=>(0,s.jsx)("div",{className:"flex-1 flex items-center w-0 h-full",children:(0,s.jsx)("span",{className:P()("cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden",{"hover:font-medium":!k}),onClick:M||(()=>f(p)),children:p})});let E=h.map(p=>(0,s.jsxs)("div",{className:"flex items-center pb-[16px]",children:[(0,s.jsx)("i",{className:"font_family icon-rizhi mr-6"}),g(p.name),(0,s.jsx)("div",{className:"text-[12px] text-[#8d8da5]",children:p.messageTime})]},p.name));if(h!=null&&h.length||(E=(0,s.jsx)(Z.Z,{})),C)switch(C.type){case"ppt":case"html":E=(0,s.jsx)(de,{htmlUrl:C.url,className:"h-full"});break;case"csv":case"xlsx":E=(0,s.jsx)(it,{fileUrl:C.url,fileName:C.name});break;default:E=(0,s.jsx)(ye,{fileUrl:C.url,fileName:C.name})}const w=(0,G.Z)(()=>xe(this,null,function*(){var M;if(!(C!=null&&C.url))return;r();const p=yield fetch(C.url);if(!p.ok)throw m(),new Error("Network response was not ok");const k=yield p.text();We(k),m(),(M=se())==null||M.success("\u590D\u5236\u6210\u529F")}));return(0,s.jsx)(Me,{className:"p-16 overflow-y-auto",titleNode:C&&(0,s.jsxs)(s.Fragment,{children:[g(C==null?void 0:C.name,!0,u),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(q.Z,{title:"\u4E0B\u8F7D",children:(0,s.jsx)("i",{className:"font_family rounded-[4px] size-20 flex items-center justify-center icon-xiazai mr-6 cursor-pointer hover:bg-gray-200",onClick:()=>Yt(C==null?void 0:C.url.replace("preview","download"),C.name)})}),!["xlsx","xls"].includes(C.type)&&(0,s.jsx)(q.Z,{title:"\u590D\u5236",placement:"top",children:d?(0,s.jsx)(ae,{}):(0,s.jsx)("i",{className:"font_family rounded-[4px] size-20 flex items-center justify-center icon-fuzhi cursor-pointer hover:bg-gray-200",onClick:w})})]})]}),onClickTitle:()=>f(void 0),children:E})},Ms=a=>{const{className:n}=a;return(0,s.jsx)("div",{className:P()("size-14 bg-primary inline-block rounded-[50%]",n)})},ut=a=>{switch(a){case"not_started":return(0,s.jsx)(Ms,{className:"animate-pulse"});case"in_progress":return(0,s.jsx)(ae,{});case"completed":return(0,s.jsx)(Ot.Z,{className:"text-primary"});default:return null}},Is=a=>{const{title:n,status:l,className:i}=a,[o,f]=(0,ge.Z)(),d=(0,A.useRef)({}),m=(0,G.Z)(r=>{clearTimeout(d.current.t),l==="in_progress"?(f(r!=null?r:(o||0)+1),d.current.t=setTimeout(()=>{m()},1e3)):f(void 0)});return(0,A.useEffect)(()=>{l==="in_progress"&&n&&m(0)},[l,n,m]),(0,A.useEffect)(()=>()=>{clearTimeout(d.current.t)},[]),(0,s.jsxs)("div",{className:P()("w-full rounded-[12px] mt-[8px] flex items-center",i),children:[(0,s.jsx)("span",{className:"mr-10",children:ut(l)}),n,typeof o=="number"&&l==="in_progress"&&(0,s.jsxs)("span",{className:"text-[12px] ml-8 text-[#848581]",children:[qt(o),(0,s.jsx)("span",{className:"ml-4",children:"\u6267\u884C\u4E2D"})]})]})},Ts=(0,A.forwardRef)((a,n)=>{const{plan:l}=a,{stages:i,stepStatus:o,steps:f}=l||{},d=(i==null?void 0:i.reduce((w,p,k,M)=>(o==null?void 0:o[k])==="completed"?Math.min(k+1,M.length-1):w,0))||0,m=o==null?void 0:o[d],r=i==null?void 0:i[d],[u,{toggle:h,setLeft:I,setRight:C}]=(0,St.Z)(!1),g=(0,A.useRef)(null);(0,A.useImperativeHandle)(n,()=>({openPlanView:C,closePlanView:I,togglePlanView:h}));const E=w=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center h-32",children:["\u4EFB\u52A1\u8FDB\u5EA6",(0,s.jsxs)("div",{className:"ml-auto flex items-center text-[#848581]",children:[(0,s.jsxs)("span",{className:"mr-4 text-[12px]",children:[d+1," / ",i==null?void 0:i.length]}),(0,s.jsx)("i",{className:P()("transition-all font_family icon-shouqi size-16 flex items-center justify-center hover:bg-gray-300 rounded-[4px] cursor-pointer",{"rotate-z-180":u}),onClick:h})]})]}),(0,s.jsx)(Is,{title:r,status:m,className:P()({hidden:!w})})]});return(0,A.useEffect)(()=>{const w=(0,te.throttle)(()=>{var N,B;if(!g.current||!l)return;const k=(N=g.current)==null?void 0:N.querySelector(".plan-item");let M=((k==null?void 0:k.clientHeight)||63)+32;u&&(M=(B=g.current)==null?void 0:B.scrollHeight),g.current.style.height=`${M}px`},30);w();const p=new ResizeObserver(w);return g.current&&p.observe(g.current),()=>{p.disconnect()}},[l,u]),l?(0,s.jsxs)("div",{className:"w-full border-[#e9e9f0] mt-[16px] p-[16px] relative",children:[(0,s.jsx)("div",{className:"opacity-0",children:E(!0)}),(0,s.jsxs)("div",{className:P()("w-full rounded-[12px] border-solid border-1 border-[#e9e9f0] mt-[16px] p-[16px] bg-[#fff]","absolute bottom-0 left-0","transition-all duration-300 overflow-hidden"),ref:g,children:[(0,s.jsx)("div",{className:"plan-item",children:E(!u)}),u&&(0,s.jsx)(Nt.Z,{className:P()("px-12 pb-0 pt-32 bg-[#f9f9fc] rounded-[6px] transition-all duration-300"),items:i==null?void 0:i.map((w,p)=>{const k=o==null?void 0:o[p],M=f==null?void 0:f[p];return{dot:ut(k),children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-[#80d1ee6]",children:w}),(0,s.jsx)("div",{className:"text-gray text-[12px] text-[#2029459e]",children:M})]}),key:w}})})]})]}):null}),bs=(0,A.forwardRef)((a,n)=>{const{className:l,onClose:i,title:o,activeTask:f,taskList:d,plan:m}=a,[r,u]=(0,ge.Z)(),h=(0,A.useRef)(null),{defaultActiveActionView:I,actionViewOptions:C}=(0,A.useContext)(lt),[g,E]=(0,ge.Z)(I);return(0,A.useImperativeHandle)(n,()=>K(O({},h.current),{setFilePreview:w=>{E(H.file),u(w)},changeActionView:E})),(0,s.jsxs)("div",{className:P()("p-24 pt-8 pb-24 w-full h-full flex flex-col",l),children:[(0,s.jsx)(hs,{onClose:i,children:o||"\u5DE5\u4F5C\u7A7A\u95F4"}),(0,s.jsx)(fs,{value:g,onChange:E,options:C}),(0,s.jsxs)("div",{className:"mt-12 flex-1 h-0 flex flex-col",children:[(0,s.jsx)(ws,{taskItem:f,taskList:d,className:P()({hidden:g!==H.follow})}),g===H.browser&&(0,s.jsx)(Ns,{taskList:d}),g===H.file&&(0,s.jsx)(Cs,{taskList:d,activeFile:r,clearActiveFile:()=>{u(void 0)}})]}),(0,s.jsx)(Ts,{plan:m,ref:h})]})}),Ie=bs;Ie.useActionView=()=>(0,A.useRef)(null);const Bs=a=>{const{inputInfo:n,product:l}=a,[i,o]=(0,A.useState)(""),[f,d]=(0,A.useState)([]),m=(0,A.useRef)([]),[r,u]=(0,A.useState)(),[h,I]=(0,A.useState)(),[C,g]=(0,A.useState)(!1),[E,w]=(0,A.useState)(!1),p=(0,A.useRef)(null),k=Ie.useActionView(),M=(0,A.useMemo)(()=>$t(),[]),[N,B]=_e.Z.useModal(),b=(0,G.Z)(_=>{const{message:z,deepThink:le,outputStyle:be}=_,re=Qe();let Q=((J,V,W)=>({query:J.message,files:J.files,responseType:"txt",sessionId:V,requestId:W,loading:!0,forceStop:!1,tasks:[],thought:"",response:"",taskStatus:0,tip:"\u5DF2\u63A5\u6536\u5230\u4F60\u7684\u4EFB\u52A1\uFF0C\u5C06\u7ACB\u5373\u5F00\u59CB\u5904\u7406...",multiAgent:{tasks:[]}}))(_,M,re);m.current=[...m.current,Q],i||o(z),w(!0);const Be=J=>{J.filter(V=>!at.includes(V.messageType)).length&&g(!0)};((J,V="https://***********:13000/web/api/v1/gpt/queryAgentStreamIncr")=>{const{body:W=null,handleMessage:Re,handleError:me,handleClose:ee}=J;(0,Ft.L)(V,{method:"POST",credentials:"include",headers:ls,body:JSON.stringify(W),openWhenHidden:!0,onmessage(Y){if(Y.data)try{const Pe=JSON.parse(Y.data);Re(Pe)}catch(Pe){console.error("Error parsing SSE message:",Pe),me(new Error("Failed to parse SSE message"))}},onerror(Y){console.error("SSE error:",Y),me(Y)},onclose(){console.log("SSE connection closed"),ee()}})})({body:{sessionId:M,requestId:re,query:z,deepThink:le?1:0,outputStyle:be},handleMessage:J=>{const{finished:V,resultMap:W,packageType:Re,status:me}=J;if(me==="tokenUseUp"){N.info({title:"\u60A8\u7684\u8BD5\u7528\u6B21\u6570\u5DF2\u7528\u5C3D",content:"\u5982\u9700\u989D\u5916\u7533\u8BF7\uFF0C\u8BF7\u8054\u7CFB <EMAIL>"});const ee=qe(Q,le,Q.multiAgent);return Q.loading=!1,w(!1),void d(ee.taskList)}Re!=="heartbeat"&&(requestAnimationFrame(()=>{if(W!=null&&W.eventData){Q=rs(W.eventData||{},Q);const ee=qe(Q,le,Q.multiAgent);d(ee.taskList),R(ee.plan),Be(ee.taskList),V&&(Q.loading=!1,w(!1));const Y=[...m.current];Y.splice(Y.length-1,1,Q),m.current=Y}}),Gt(p.current))},handleError:J=>{throw J},handleClose:()=>{console.log("\u{1F680} ~ close")}})}),T=_=>{var z;(z=k.current)==null||z.changeActionView(H.follow),L(!0),u(_)},R=_=>{I(_)},D=_=>{var z;L(!0),(z=k.current)==null||z.setFilePreview(_)},U=()=>{var _;L(!0),(_=k.current)==null||_.openPlanView()},L=_=>{g(_)};return(0,A.useEffect)(()=>{var _;((_=n.message)==null?void 0:_.length)!==0&&b(n)},[n,b]),(0,s.jsxs)("div",{className:"h-full w-full flex justify-center",children:[(0,s.jsxs)("div",{className:P()("p-24 flex flex-col flex-1 w-0",{"max-w-[1200px]":!C}),id:"chat-view",children:[(0,s.jsx)("div",{className:"w-full flex justify-between",children:(0,s.jsxs)("div",{className:"w-full flex items-center pb-8",children:[(0,s.jsx)("div",{className:"overflow-hidden whitespace-nowrap text-ellipsis text-[16px] font-[500] text-[#27272A] mr-8",children:i}),n.deepThink&&(0,s.jsxs)("div",{className:"rounded-[4px] px-6 flex items-center shrink-0",style:{border:"1px solid rgba(64,64,255,0.2)"},children:[(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z",fill:"currentColor",stroke:"currentColor","stroke-width":".1"}),(0,s.jsx)("path",{d:"M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z",fill:"currentColor",stroke:"currentColor","stroke-width":".2"})]}),(0,s.jsx)("span",{className:"ml-[4px]",children:"\u6DF1\u5EA6\u7814\u7A76"})]})]})}),(0,s.jsx)("div",{className:"w-full flex-1 overflow-auto no-scrollbar mb-[36px]",ref:p,children:m.current.map(_=>(0,s.jsx)("div",{children:(0,s.jsx)(ps,{chat:_,deepThink:n.deepThink,changeTask:T,changeFile:D,changePlan:U})},_.requestId))}),(0,s.jsx)(Xe,{placeholder:E?"\u4EFB\u52A1\u8FDB\u884C\u4E2D":"\u5E0C\u671B GoData \u4E3A\u4F60\u505A\u54EA\u4E9B\u4EFB\u52A1\u5462\uFF1F",showBtn:!1,size:"medium",disabled:E,product:l,send:_=>b(K(O({},_),{deepThink:n.deepThink}))})]}),B,(0,s.jsx)("div",{className:P()("transition-all w-0",{"opacity-0 overflow-hidden":!C,"flex-1":C}),children:(0,s.jsx)(Ie,{activeTask:r,taskList:f,plan:h,ref:k,onClose:()=>L(!1)})})]})},xt={baseURL:"",apiPrefix:"/web",timeout:1e4,headers:{"Content-Type":"application/json"}},pt=(0,A.createContext)({config:xt,updateConfig:()=>{}}),Rs=({children:a,config:n={}})=>{const[l,i]=A.useState(O(O({},xt),n)),o=A.useCallback(d=>{i(m=>O(O({},m),d))},[]),f=A.useMemo(()=>({config:l,updateConfig:o}),[l,o]);return(0,s.jsx)(pt.Provider,{value:f,children:a})},Zs=()=>{const a=c(pt);if(!a)throw new Error("useServiceConfig must be used within a ServiceProvider");return a},Ps={baseURL:window.SERVICE_BASE_URL||"",timeout:1e4,headers:{"Content-Type":"application/json"}},Te=Vt.Z.create(Ps),_s=a=>{Object.assign(Te.defaults,a)};Te.interceptors.request.use(a=>a,a=>(console.error("\u8BF7\u6C42\u9519\u8BEF:",a),Promise.reject(a)));const ht=a=>{var n;(n=se())==null||n.error("\u672A\u767B\u5F55"),a&&(location.href=a)};Te.interceptors.response.use(a=>{var i;const{data:n,status:l}=a;if(l===200){if(n.code===200)return n.data;if(n.code!==401)return(i=se())==null||i.error(n.msg||"\u8BF7\u6C42\u5931\u8D25"),Promise.reject(new Error(n.msg||"\u8BF7\u6C42\u5931\u8D25"));ht(n.redirectUrl)}return a},a=>{console.error("\u54CD\u5E94\u9519\u8BEF:",a);const n=se();if(a.response){const{status:l,data:i}=a.response;switch(l){case 401:ht(i.redirectUrl);break;case 403:n==null||n.error(a.message||"\u6CA1\u6709\u6743\u9650\u8BBF\u95EE");break;case 404:n==null||n.error(a.message||"\u8BF7\u6C42\u7684\u8D44\u6E90\u4E0D\u5B58\u5728");break;case 500:n==null||n.error(a.message||"\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF");break;default:n==null||n.error(a.message||`\u8BF7\u6C42\u5931\u8D25\uFF0C\u72B6\u6001\u7801: ${l}`)}}else a.request?n==null||n.error(a.message||"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5"):n==null||n.error("\u8BF7\u6C42\u914D\u7F6E\u9519\u8BEF");return Promise.reject(a)});const ft=(0,A.memo)(a=>{const{className:n,style:l,initialProduct:i,customProductList:o,customDemoList:f,placeholder:d,showLogo:m=!0,customLogo:r,theme:u,serviceConfig:h}=a;(0,A.useEffect)(()=>{h&&_s({baseURL:h.baseURL,timeout:h.timeout,headers:h.headers})},[h]);const[I,C]=(0,A.useState)({message:"",deepThink:!1}),[g,E]=(0,A.useState)(i||st),w=(0,A.useCallback)(k=>{C(k)},[]),p=u?{token:{colorPrimary:u.primaryColor||"#4040ff",borderRadius:u.borderRadius||8}}:{};return(0,s.jsx)(Rs,{config:h,children:(0,s.jsx)(yt.ZP,{locale:Ct.Z,theme:p,children:(0,s.jsx)("div",{className:`h-full flex flex-col items-center justify-center ${n||""}`,style:l,children:I.message.length===0?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[m&&(r||(0,s.jsx)(ns,{})),(0,s.jsx)("div",{className:"w-640 rounded-xl shadow-[0_18px_39px_0_rgba(198,202,240,0.1)]",children:(0,s.jsx)(Xe,{placeholder:d||g.placeholder,showBtn:!0,size:"big",disabled:!1,product:g,send:w})}),(0,s.jsx)("div",{className:"w-640 flex flex-wrap gap-16 mt-[16px]",children:(o||ve).map((k,M)=>{const N=k.type===g.type;return(0,s.jsx)("div",{className:"w-[22%] h-[36px] cursor-pointer flex items-center justify-center border rounded-lg transition-all duration-200 "+(N?"border-blue-500 text-blue-500":"border-gray-200 text-gray-600"),onClick:()=>E(k),children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:`text-14 ${k.color} flex items-center`,children:(B=k.img,{"icon-diannao":(0,s.jsx)(oe.Z,{}),"icon-wendang":(0,s.jsx)(De.Z,{}),"icon-ppt":(0,s.jsx)(Oe.Z,{}),"icon-biaoge":(0,s.jsx)(Ue.Z,{})}[B]||(0,s.jsx)(oe.Z,{}))}),(0,s.jsx)("div",{className:"ml-[6px]",children:k.name})]})},M);var B})})]}):(0,s.jsx)(Bs,{inputInfo:I,product:g})})})})});ft.displayName="GenieUI";const Qs=a=>{const{className:n,hideSplit:l}=a;return t("div",{className:j("flex items-center",n),children:[e("img",{src:"assets/logo.423cf454be0b5684.png",alt:"logo",width:20}),e("div",{className:"ml-8 text-lg font-bold text-transparent bg-clip-text text-[16px]",style:{backgroundImage:"linear-gradient(270deg, rgba(130,45,255,1) 0%,rgba(62,69,255,1) 20.88266611099243%,rgba(60,196,250,1) 100%)"},children:"Genie"}),!l&&e("div",{className:"w-1 h-16 mx-8 bg-[#dbdbde]"})]})},Js=()=>{const a=ne();return e(y,{status:"404",title:"404",subTitle:"\u62B1\u6B49\uFF0C\u60A8\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728\u3002",extra:e(x,{type:"primary",onClick:()=>a("/"),children:"\u8FD4\u56DE\u9996\u9875"})})}},21916:function(){}}]);
}());