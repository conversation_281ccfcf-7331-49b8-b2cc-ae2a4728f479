.agentList {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 248px;
  height: 100%;
  background: linear-gradient(180deg, #fafbff 0%, #f5f7ff 100%);
  border-right: 1px solid #e5e7eb;
  transition: width 0.3s ease, opacity 0.3s ease;
  overflow: visible; /* 确保下拉框可以正常显示 */

  // 统一的滚动条样式
  * {
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.3);
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.5);
      }
    }

    /* 隐藏滚动条的上下箭头按钮 */
    &::-webkit-scrollbar-button {
      display: none !important;
      height: 0 !important;
      width: 0 !important;
    }
  }

  .addBtnContent {
    padding: 12px 16px;

    .newChatButton {
      width: 100%;
      height: 36px;
      border-radius: 12px;
      background: linear-gradient(90deg, #EAF1FF 0%, #EEEBFD 100%);
      border: none;
      font-weight: 600;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(94, 149, 255, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      cursor: pointer;

      // 文字渐变色效果 - 使用span包装文字内容
      span {
        background: linear-gradient(90deg, #5E95FF 0%, #3A7DFF 33%, #665CF4 66%, #8B66F4 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        font-weight: inherit;
        font-size: inherit;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
        z-index: 1;
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(94, 149, 255, 0.25);
        background: linear-gradient(90deg, #E3EFFF 0%, #E8E4FD 100%);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(94, 149, 255, 0.2);
        background: linear-gradient(90deg, #DCE9FF 0%, #E1DCFD 100%);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(94, 149, 255, 0.3);
      }

      .newChatIcon {
        // margin-right: 5px;
        position: relative;
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        .iconBg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          z-index: 1;
        }
      }
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    // padding: 8px;
    padding: 16px;

    .headerTitle {
      color: var(--text-color);
      font-weight: 500;
      font-size: 16px;
    }

    .plusIcon {
      color: var(--text-color);
      font-size: 15px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }
  }

  .agentListContent {
    max-height: 235px;
    display: flex;
    flex-direction: column;
    padding: 0 16px;
    row-gap: 2px;
    // height: calc(100% - 50px);
    overflow-y: auto;

    .agentSelect {
      margin-top: 8px;
      padding: 12px 16px;
      height: auto;
      width: 100%; // 固定宽度，与新对话按钮保持一致
      border-radius: 10px;
      background: #ffffff;
      border: 1px solid #e5e7eb;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      .agentSelectContent {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .currentAgentIcon {
          color: #667eea;
          font-size: 16px;
          margin-right: 8px;
        }

        .chatHeaderTitle {
          flex: 1;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 140px;
        }

        .dropdownArrow {
          color: #9ca3af;
          font-size: 12px;
          transition: transform 0.3s ease;
        }
      }

      &:hover {
        border-color: #667eea;
        box-shadow: 0 6px 16px rgba(102, 126, 234, 0.2);
        transform: translateY(-1px);

        .currentAgentIcon {
          color: #5a6fd8;
        }

        .chatHeaderTitle {
          color: #667eea;
        }

        .dropdownArrow {
          color: #667eea;
          transform: rotate(180deg);
        }
      }
    }

    .agentItem {
      display: flex;
      align-items: center;
      padding: 8px 4px;
      column-gap: 8px;
      border-radius: 8px;
      cursor: pointer;

      .avatar {
        font-size: 40px;
      }

      .agentInfo {
        display: flex;
        flex-direction: column;
        row-gap: 2px;

        .agentName {
          color: #000;
          font-size: 14px;
        }

        .agentDesc {
          width: 160px;
          overflow: hidden;
          color: var(--text-color-fourth);
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &:hover,
      &.active {
        //background: #22a5f7;
        // background: #eb9999;
        background: #e5e5e5;

        .agentName{
          color: #000;
        }
        .agentDesc {
          color: #939393;
        }
      }
    }
  }
  .historyTitle{
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 16px 16px;
    // color: var(--text-color);
    color: #171717;
    font-weight: 500;
    font-size: 15px;
  }

  .chatHistoryContent {
    width: 100%;
    flex: 1;
    padding: 8px 16px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-y: auto;
    position: relative;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.3);
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.5);
      }
    }

    /* 隐藏滚动条的上下箭头按钮 */
    &::-webkit-scrollbar-button {
      display: none !important;
      height: 0 !important;
      width: 0 !important;
    }

    // 为Firefox提供滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;

    // 当滚动条出现时，动态调整右侧padding来保持对齐
    // 这个类会通过JavaScript动态添加
    &:global(.has-scrollbar) {
      padding-right: 8px; // 减少右侧padding来补偿滚动条宽度（16px - 8px = 8px）
    }

    // 当dropdown打开时隐藏滚动条，避免与dropdown滚动条重叠
    // 同时通过padding补偿滚动条空间，避免布局抖动
    &.hideScrollbar {
      &::-webkit-scrollbar {
        display: none !important;
      }

      // 为Firefox隐藏滚动条
      scrollbar-width: none !important;

      // 为IE隐藏滚动条
      -ms-overflow-style: none !important;

      // 通过右侧padding补偿滚动条占用的空间，避免布局抖动
      // 无论是否有has-scrollbar类，都保持16px的总padding（原始padding）
      padding-right: 16px !important;

      // 如果同时有has-scrollbar类，需要覆盖其设置
      &:global(.has-scrollbar) {
        padding-right: 16px !important;
      }
    }

    .conversationList {
      display: flex;
      flex-direction: column;
      height: calc(100% - 70px);
      padding: 8px 0 0;
      row-gap: 4px;

      .timeGroup {
        margin-bottom: 16px;
      }

      .timeGroupTitle {
        padding: 8px 12px;
        color: #6b7280;
        font-size: 12px;
        font-weight: 600;
        // background: #e0f2fe;
        background: #F5F5F5;
        border-radius: 8px;
        margin: 0 0 8px 0;
        border-left: 3px solid #0ea5e9;
      }

      .conversationTime {
        font-size: 12px;
        color: #666;
      }

      .conversationItem {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 10px;
        cursor: pointer;
        margin-bottom: 4px;
        background: #ffffff;
        border: 1px solid #f1f5f9;
        transition: all 0.2s ease;

        .conversationContent {
          width: 100%;

          .topTitleBar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .conversationTitleBar {
              display: flex;
              align-items: center;
              column-gap: 6px;

              .conversationName {
                max-width: 300px;
                margin-right: 2px;
                overflow: hidden;
                color: var(--text-color);
                font-weight: 500;
                font-size: 14px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .currentConversation {
                padding: 4px 8px;
                color: #667eea;
                font-size: 11px;
                font-weight: 600;
                background: rgba(102, 126, 234, 0.1);
                border-radius: 6px;
              }
            }

            .conversationTime {
              color: #9ca3af;
              font-size: 11px;
              font-weight: 500;
            }
          }

          .bottomSection {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 3px;

            .subTitle {
              width: 350px;
              overflow: hidden;
              // color: var(--text-color-six);
              color: #262626;
              font-size: 13px;
              white-space: nowrap;
              text-overflow: ellipsis;
              line-height: 1.3;
            }

            .deleteIcon {
              display: none;
              color: #64748b;
              font-size: 14px;
              cursor: pointer;
              background-color: #f1f5f9;
              padding: 4px;
              border-radius: 6px;
              transition: all 0.2s ease;

              &:hover {
                color: #ef4444;
                background-color: #fef2f2;
              }
            }
          }

          .conversationTime {
            font-size: 11px;
            color: #666;
            margin-top: 1px;
          }
        }

        &:hover {
          background-color: #f8fafc;
          border-color: #e2e8f0;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .deleteIcon {
            display: block !important;
          }
        }

        &.activeConversationItem {
          background-color: #ffffff;
          border-color: #93c5fd;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);

          .subTitle {
            color: #1d4ed8;
            font-weight: 600;
          }

          .deleteIcon {
            display: block !important;
          }
        }
      }
    }
  }
}

.agentListHidden {
  width: 0;
  opacity: 0;
  // visibility: hidden;
}

.dropdownItem {
  max-width: 191px;
  display: flex;
  align-items: center;
  padding: 8px 0;

  .agentItemContent {
    display: flex;
    align-items: center;
    width: 100%;

    .agentIcon {
      color: #667eea;
      font-size: 16px;
      margin-right: 10px;
    }

    .agentName {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .selectedIcon {
      color: #667eea;
      font-size: 14px;
      margin-left: 8px;
    }
  }

  // 只针对 AgentList 组件内的下拉菜单，避免影响其他组件
  :global(.ant-dropdown.agent-list-dropdown) {
    .ant-dropdown-menu {
      max-width: 220px;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border: 1px solid #e5e7eb;

      .ant-dropdown-menu-item {
        padding: 12px 16px;
        border-radius: 8px;
        margin: 4px;

        &:hover {
          background-color: rgba(102, 126, 234, 0.08);
        }
      }
    }
  }
}

// 全局样式：为智能体选择下拉菜单设置宽度
:global(.agent-select-dropdown) {
  max-height: 600px !important; /* 直接在容器上设置最大高度 */
  overflow-y: auto !important; /* 直接在容器上设置滚动 */

  .ant-dropdown-menu {
    width: 100% !important; /* 与触发器宽度保持一致 */
    min-width: 100% !important;
    max-width: 100% !important;
    max-height: 600px !important; /* 设置最大高度，防止被遮挡 */
    overflow-y: auto !important; /* 内容超出时显示滚动条 */
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;

    /* 自定义滚动条样式 */
    // &::-webkit-scrollbar {
    //   width: 6px !important;
    // }

    // &::-webkit-scrollbar-track {
    //   background: transparent !important;
    //   border-radius: 3px !important;
    // }

    // &::-webkit-scrollbar-thumb {
    //   background: rgba(102, 126, 234, 0.3) !important;
    //   border-radius: 3px !important;
    //   transition: all 0.3s ease !important;

    //   &:hover {
    //     background: rgba(102, 126, 234, 0.5) !important;
    //   }
    // }
  }

  .ant-dropdown-menu-item {
    .dropdownItem {
      width: 100%;

      .agentItemContent {
        .agentName {
          max-width: 140px; /* 确保与主按钮中的文字宽度一致 */
        }
      }
    }
  }
}

// 防止下拉框影响整体布局的额外保护
:global(.ant-dropdown) {
  &.agent-select-dropdown {
    transform: none !important; /* 防止transform导致的布局偏移 */
    max-height: 600px !important; /* 确保下拉框不会超出视口 */
    overflow: visible !important; /* 确保内容可见 */
  }
}

// 确保下拉框在所有情况下都能正确显示 - 使用更强的选择器
:global {
  .ant-dropdown.agent-select-dropdown {
    max-height: 600px !important;
    overflow-y: auto !important;

    .ant-dropdown-menu {
      max-height: 600px !important;
      overflow-y: auto !important;

      /* 为Firefox提供滚动条样式 */
      scrollbar-width: thin !important;
      scrollbar-color: rgba(102, 126, 234, 0.3) transparent !important;
    }
  }

  // 最强优先级的样式规则 - 使用属性选择器
  div[class*="agent-select-dropdown"] {
    max-height: 600px !important;
    overflow-y: auto !important;

    .ant-dropdown-menu {
      max-height: 600px !important;
      overflow-y: auto !important;
    }
  }

  // 使用更具体的选择器
  .ant-dropdown-menu.ant-dropdown-menu-root {
    max-height: 600px !important;
    overflow-y: auto !important;
  }

  // 最强制的样式 - 使用body作为父选择器
  body .ant-dropdown.agent-select-dropdown .ant-dropdown-menu {
    max-height: 600px !important;
    overflow-y: auto !important;
  }
}

// 只针对 AgentList 相关的下拉菜单，避免影响其他组件的下拉菜单
:global(.ant-dropdown-menu.agent-list-dropdown-menu) {
  max-height: 600px !important;
  overflow-y: auto !important;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px !important;
  }

  &::-webkit-scrollbar-track {
    background: transparent !important;
    border-radius: 3px !important;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3) !important;
    border-radius: 3px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: rgba(102, 126, 234, 0.5) !important;
    }
  }

  /* 隐藏滚动条的上下箭头按钮 */
  &::-webkit-scrollbar-button {
    display: none !important;
    height: 0 !important;
    width: 0 !important;
  }
}

// 只针对 AgentList 相关的滚动条，避免影响其他组件
:global {
  .agent-list-dropdown *::-webkit-scrollbar-button,
  .agent-select-dropdown *::-webkit-scrollbar-button {
    display: none !important;
    height: 0 !important;
    width: 0 !important;
  }

  // 特别针对 AgentList 下拉菜单的滚动条
  .agent-list-dropdown .ant-dropdown-menu::-webkit-scrollbar-button,
  .agent-select-dropdown .ant-dropdown-menu::-webkit-scrollbar-button {
    display: none !important;
    height: 0 !important;
    width: 0 !important;
  }
}
