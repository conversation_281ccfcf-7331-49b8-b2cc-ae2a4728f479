import { Dropdown, But<PERSON>, MenuProps, <PERSON>lt<PERSON>, Divider } from 'antd';
import {
  DownOutlined,
  DeleteOutlined,
  MoreOutlined,
  EditOutlined,
  RobotOutlined,
  CheckCircleFilled,
} from '@ant-design/icons';
import { AgentType, ConversationDetailType } from '../type';
import styles from './style.module.less';
import classNames from 'classnames';
import { message } from 'antd';
import IconFont from '../../components/IconFont';
import { AGENT_ICONS, DEFAULT_CONVERSATION_NAME } from '../constants';
import { deleteConversation, getAllConversations, saveConversation } from '../service';
import {
  useEffect,
  useState,
  forwardRef,
  ForwardRefRenderFunction,
  useImperativeHandle,
  memo,
  useRef,
} from 'react';
import moment from 'moment';
import ConversationModal from '../components/ConversationModal';
import AddBG from '../../assets/addBG.png'

type Props = {
  agentList: AgentType[];
  currentAgent?: AgentType;
  onSelectAgent: (agent: AgentType) => void;
  currentConversation?: ConversationDetailType;
  onSelectConversation: (
    conversation: ConversationDetailType,
    sendMsgParams?: any,
    isAdd?: boolean
  ) => void;
  visible: boolean;
  conversations?: ConversationDetailType[];
  updateConversations?: (agentId?: number) => Promise<ConversationDetailType[]>;
};

const AgentList: ForwardRefRenderFunction<any, Props> = (
  { agentList, currentAgent, onSelectAgent, currentConversation, onSelectConversation, visible = true, conversations: externalConversations, updateConversations }
  ,ref) => {
    const [conversations, setConversations] = useState<ConversationDetailType[]>([]);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [editConversation, setEditConversation] = useState<ConversationDetailType>();
    const [searchValue, setSearchValue] = useState('');
  const [hasScrollbar, setHasScrollbar] = useState(false);
  const [openDropdownCount, setOpenDropdownCount] = useState(0);
  const chatHistoryRef = useRef<HTMLDivElement>(null);
  
    useImperativeHandle(ref, () => ({
      updateData: updateConversations,
      onAddConversation,
    }));

    // 使用外部传入的conversations数据，避免重复调用getAllConversations
    useEffect(() => {
      if (externalConversations) {
        setConversations(externalConversations);
      }
    }, [externalConversations]);
  
    // 检测滚动条的useEffect
    useEffect(() => {
      const checkScrollbar = () => {
        if (chatHistoryRef.current) {
          const element = chatHistoryRef.current;
          const hasVerticalScrollbar = element.scrollHeight > element.clientHeight;
          setHasScrollbar(hasVerticalScrollbar);
        }
      };

      // 初始检测
      checkScrollbar();

      // 监听窗口大小变化
      window.addEventListener('resize', checkScrollbar);

      // 监听内容变化（当对话列表更新时）
      const observer = new MutationObserver(checkScrollbar);
      if (chatHistoryRef.current) {
        observer.observe(chatHistoryRef.current, {
          childList: true,
          subtree: true,
        });
      }

      return () => {
        window.removeEventListener('resize', checkScrollbar);
        observer.disconnect();
      };
    }, [conversations]); // 依赖conversations，当对话列表变化时重新检测
  
    const addConversation = async (sendMsgParams?: any) => {
      const agentId = sendMsgParams?.agentId || currentAgent!.id;
      await saveConversation(DEFAULT_CONVERSATION_NAME, agentId);
      if (updateConversations) {
        return updateConversations(agentId);
      }
      return [];
    };

    const onDeleteConversation = async (id: number) => {
      await deleteConversation(id);
      if (updateConversations) {
        const data = await updateConversations(currentAgent?.id);
        if (data.length > 0) {
          onSelectConversation(data[0]);
        } else {
          onAddConversation();
        }
      }
    };
  
    const onAddConversation = async (sendMsgParams?: any) => {
      const data = await addConversation(sendMsgParams);
      if (data.length > 0) {
        onSelectConversation(data[0], sendMsgParams, true);
      }
    };
  
    const onOperate = (key: string, conversation: ConversationDetailType) => {
      if (key === 'editName') {
        setEditConversation(conversation);
        setEditModalVisible(true);
      } else if (key === 'delete') {
        onDeleteConversation(conversation.chatId);
      }
    };
  
    // const conversationClass = classNames(styles.conversation, {
    //   [styles.historyVisible]: historyVisible,
    // });
  
    const convertTime = (date: string) => {
      moment.locale('zh-cn');
      const now = moment();
      const inputDate = moment(date);
      const yesterday = moment().subtract(1, 'day');
      const sevenDaysAgo = moment().subtract(7, 'days');

      if (inputDate.isSame(now, 'day')) {
        // 今天：显示 HH:mm
        return inputDate.format('HH:mm');
      } else if (inputDate.isSame(yesterday, 'day')) {
        // 昨天：显示 MM-DD HH:mm
        return inputDate.format('MM-DD HH:mm');
      } else if (inputDate.isAfter(sevenDaysAgo)) {
        // 7天内：显示 MM-DD HH:mm
        return inputDate.format('MM-DD HH:mm');
      } else {
        // 更早：显示 YYYY-MM-DD HH:mm
        return inputDate.format('YYYY-MM-DD HH:mm');
      }
    };
  
    const onSearchValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchValue(e.target.value);
    };
  const onAddAgent = () => {
    message.info('正在开发中，敬请期待');
  };

  // 处理dropdown打开/关闭状态
  const handleDropdownOpenChange = (open: boolean) => {
    setOpenDropdownCount(prev => open ? prev + 1 : Math.max(0, prev - 1));
  };

  const agentListClass = classNames(styles.agentList, {
    [styles.agentListHidden]: !visible
  })


  // 构建 Dropdown 菜单项
  const getAgentMenuItems = (): MenuProps['items'] => {
    return agentList.map((agent) => ({
      key: agent.id.toString(),
      label: (
         <Tooltip
            title={`${agent.name}`}
            placement="topLeft"
            mouseEnterDelay={0.5}
          >
          <div className={styles.dropdownItem}>
            <div className={styles.agentItemContent}>
              <RobotOutlined className={styles.agentIcon} />
              <div className={styles.agentName}>
                {agent.name}
              </div>
              {currentAgent?.id === agent.id && (
                <CheckCircleFilled className={styles.selectedIcon} />
              )}
            </div>
            {/* <div style={{ fontSize: '12px', color: '#999' }}>
              {agent.description}
            </div>   */}
          </div>
          </Tooltip>
      ),
      onClick: () => onSelectAgent(agent),
    }));
  };
    
  // 处理 Dropdown 菜单点击  
  const handleAgentMenuClick: MenuProps['onClick'] = ({ key }) => {  
    const selectedAgent = agentList.find(agent => agent.id.toString() === key);  
    if (selectedAgent) {  
      onSelectAgent(selectedAgent);  
    }  
  };

  // 获取时间分组
  const getTimeGroup = (dateString: string) => {
    const today = moment().startOf('day');
    const yesterday = moment().subtract(1, 'day').startOf('day');
    const last7Days = moment().subtract(7, 'days').startOf('day');

    const date = moment(dateString);
    if (date.isSameOrAfter(today)) {
      return 'today';
    } else if (date.isSameOrAfter(yesterday)) {
      return 'yesterday';
    } else if (date.isSameOrAfter(last7Days)) {
      return 'last7Days';
    } else {
      return 'earlier';
    }
  };


  return (
    <div className={agentListClass}>
      {/* <div className={styles.header}>
        <div className={styles.headerTitle}>智能体</div>
      </div> */}
      <div className={styles.addBtnContent}>
        <Button
          type="primary"
          className={styles.newChatButton}
          onClick={() => {
            // addConversation();
            onAddConversation();
          }}
        >
          <div className={styles.newChatIcon}>
            <img src={AddBG} alt="" className={styles.iconBg} />
          </div>
          <span>新对话</span>
        </Button>
      </div>
      <div className={styles.agentListContent}>
                  {/* trigger={['click']}
          onClick={(key)=> {
            console.log('key',key);
          }} */}
        <Dropdown
          menu={{
            items: getAgentMenuItems(),
            onClick: handleAgentMenuClick,
            style: {
              maxHeight: '600px',
              overflowY: 'auto'
            }
          }}
          trigger={['click']}
          placement="bottomLeft"
          className="agent-select-dropdown agent-list-dropdown"
          arrow
          autoAdjustOverflow={true}
          overlayStyle={{
            maxHeight: '600px',
            overflowY: 'auto'
          }}
          onOpenChange={handleDropdownOpenChange}
        >
          <Button
            type="text"
            className={styles.agentSelect}
          >
            <div className={styles.agentSelectContent}>
              <RobotOutlined className={styles.currentAgentIcon} />
              <Tooltip
                title={currentAgent?.name}
                placement="top"
                mouseEnterDelay={0.5}
              >
                <div className={styles.chatHeaderTitle}>{currentAgent?.name}</div>
              </Tooltip>
              <DownOutlined className={styles.dropdownArrow} />
            </div>
          </Button>
        </Dropdown>
        <Divider size="small"/>
      </div>
      {/* <div className={styles.historyTitle}>
        <div>历史对话<span style={{marginLeft: '8px'}}><HistoryOutlined /></span></div>
      </div> */}
      {
        currentAgent?.enableMultiAgent !== 1 && (
          <div
            ref={chatHistoryRef}
            className={classNames(styles.chatHistoryContent, {
              'has-scrollbar': hasScrollbar,
              [styles.hideScrollbar]: openDropdownCount > 0
            })}
          >
            <div className={styles.conversationList}>
              {(() => {
                // 过滤对话项
                const filteredConversations = conversations.filter(
                  conversation =>
                    searchValue === '' ||
                    conversation.chatName.toLowerCase().includes(searchValue.toLowerCase())
                );

                // 按时间分组
                const groupedConversations: Record<string, ConversationDetailType[]> = {
                  today: [],
                  yesterday: [],
                  last7Days: [],
                  earlier: []
                };

                filteredConversations.forEach(item => {
                  const group = getTimeGroup(item.lastTime || '');
                  groupedConversations[group].push(item);
                });

                // 分组标题映射
                const groupTitles = {
                  today: '今天',
                  yesterday: '昨天',
                  last7Days: '7天内',
                  earlier: '更早'
                };

                // 分组顺序
                const groupOrder: (keyof typeof groupedConversations)[] = [
                  'today', 'yesterday', 'last7Days', 'earlier'
                ];

                return (
                  <>
                    {groupOrder.map(groupKey => {
                      const groupItems = groupedConversations[groupKey];
                      if (groupItems.length === 0) return null;
                      
                      return (
                        <div key={groupKey} className={styles.timeGroup}>
                          <div className={styles.timeGroupTitle}>{groupTitles[groupKey]}</div>
                          {groupItems.map(item => {
                            const conversationItemClass = classNames(styles.conversationItem, {
                              [styles.activeConversationItem]: currentConversation?.chatId === item.chatId,
                            });
                            
                            return (
                                <div
                                  key={item.chatId}
                                  className={conversationItemClass}
                                  onClick={() => {
                                    onSelectConversation(item);
                                  }}
                                >
                                  <div className={styles.conversationContent}>
                                    {/* 显示对话时间 */}
                                    <div className={styles.bottomSection}>
                                      <div className={styles.subTitle}>{item.chatName}</div>
                                      <Dropdown
                                        key={item.chatId}
                                        className="agent-list-dropdown"
                                        menu={{
                                          items: [
                                            {
                                              label: '重命名',
                                              key: 'editName',
                                              icon: <EditOutlined />,
                                              onClick: ({ domEvent }) => {
                                                domEvent.stopPropagation();
                                                onOperate('editName', item);
                                              }
                                            },
                                            {
                                              label: '删除',
                                              key: 'delete',
                                              icon: <DeleteOutlined />,
                                              onClick: ({ domEvent }) => {
                                                domEvent.stopPropagation();
                                                onOperate('delete', item);
                                              }
                                            },
                                          ]
                                        }}
                                        trigger={['click']}
                                        onOpenChange={handleDropdownOpenChange}
                                      >
                                        <MoreOutlined
                                          className={styles.deleteIcon}
                                          onClick={e => {
                                            e.stopPropagation();
                                            e.preventDefault();
                                          }}
                                        />
                                      </Dropdown>
                                    </div>
                                    <div className={styles.conversationTime}>
                                      {convertTime(item.lastTime || '')}
                                    </div>
                                  </div>
                                </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  </>
                );
              })()}
            </div>
          </div>
        )
      }
      <ConversationModal
        visible={editModalVisible}
        editConversation={editConversation}
        onClose={() => {
          setEditModalVisible(false);
        }}
        onFinish={() => {
          setEditModalVisible(false);
          if (updateConversations) {
            updateConversations(currentAgent?.id);
          }
        }}
      />
    </div>
  );
};

export default forwardRef(AgentList);
