<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <% for (const meta of metas) { %>
  <meta name="<%= meta.name %>" content="<%= meta.content %>">
  <% } %>
  <link rel="shortcut icon" href="<%= context.config.publicPath %>favicon.ico">
  <title><%= context.config.title || 'GoData' %></title>
  
  <!-- 预加载关键资源 -->
  <link rel="preload" href="<%= context.config.publicPath %>react.<%= context.config.hash ? '[contenthash:8].' : '' %>js" as="script">
  <link rel="preload" href="<%= context.config.publicPath %>antd-core.<%= context.config.hash ? '[contenthash:8].' : '' %>js" as="script">
  <link rel="prefetch" href="<%= context.config.publicPath %>antd-others.<%= context.config.hash ? '[contenthash:8].' : '' %>js" as="script">
  <link rel="prefetch" href="<%= context.config.publicPath %>echarts.<%= context.config.hash ? '[contenthash:8].' : '' %>js" as="script">
  
  <!-- 内联关键CSS -->
  <% for (const css of styles) { %>
  <link rel="stylesheet" href="<%= css %>">
  <% } %>
  
  <!-- 内联loading脚本 - 确保最优先执行 -->
  <script>
    /**
     * loading 占位 - 内联版本
     * 解决首次加载时白屏的问题
     */
    (function () {
      const _root = document.querySelector('#root');
      if (_root && _root.innerHTML === '') {
        _root.innerHTML = `
          <style>
            html, body, #root {
              height: 100%;
              margin: 0;
              padding: 0;
            }
            #root {
              background-repeat: no-repeat;
              background-size: 100% auto;
            }
            .loading-title {
              font-size: 1.1rem;
            }
            .loading-sub-title {
              margin-top: 20px;
              font-size: 1rem;
              color: #888;
            }
            .page-loading-warp {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 26px;
            }
            .ant-spin {
              position: absolute;
              display: none;
              box-sizing: border-box;
              margin: 0;
              padding: 0;
              color: #1890ff;
              font-size: 14px;
              font-variant: tabular-nums;
              line-height: 1.5;
              text-align: center;
              list-style: none;
              opacity: 0;
              transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
              font-feature-settings: "tnum";
            }
            .ant-spin-spinning {
              position: static;
              display: inline-block;
              opacity: 1;
            }
            .ant-spin-dot {
              position: relative;
              display: inline-block;
              width: 20px;
              height: 20px;
              font-size: 20px;
            }
            .ant-spin-dot-item {
              position: absolute;
              display: block;
              width: 9px;
              height: 9px;
              background-color: #1890ff;
              border-radius: 100%;
              transform: scale(0.75);
              transform-origin: 50% 50%;
              opacity: 0.3;
              animation: antSpinMove 1s infinite linear alternate;
            }
            .ant-spin-dot-item:nth-child(1) {
              top: 0;
              left: 0;
            }
            .ant-spin-dot-item:nth-child(2) {
              top: 0;
              right: 0;
              animation-delay: 0.4s;
            }
            .ant-spin-dot-item:nth-child(3) {
              right: 0;
              bottom: 0;
              animation-delay: 0.8s;
            }
            .ant-spin-dot-item:nth-child(4) {
              bottom: 0;
              left: 0;
              animation-delay: 1.2s;
            }
            .ant-spin-dot-spin {
              transform: rotate(45deg);
              animation: antRotate 1.2s infinite linear;
            }
            .ant-spin-lg .ant-spin-dot {
              width: 32px;
              height: 32px;
              font-size: 32px;
            }
            .ant-spin-lg .ant-spin-dot i {
              width: 14px;
              height: 14px;
            }
            @keyframes antSpinMove {
              to { opacity: 1; }
            }
            @keyframes antRotate {
              to { transform: rotate(405deg); }
            }
          </style>
          <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 362px;
          ">
            <div class="page-loading-warp">
              <div class="ant-spin ant-spin-lg ant-spin-spinning">
                <span class="ant-spin-dot ant-spin-dot-spin">
                  <i class="ant-spin-dot-item"></i>
                  <i class="ant-spin-dot-item"></i>
                  <i class="ant-spin-dot-item"></i>
                  <i class="ant-spin-dot-item"></i>
                </span>
              </div>
            </div>
          </div>
        `;
      }
    })();
  </script>
  
  <!-- 关键脚本 - 同步加载 -->
  <% for (const script of headScripts.filter(s => s.critical)) { %>
  <script src="<%= script.src %>"></script>
  <% } %>
</head>
<body>
  <div id="root"></div>
  
  <!-- 主要脚本 - 延迟加载 -->
  <% for (const script of scripts) { %>
  <script <% if (script.defer !== false) { %>defer<% } %> src="<%= script %>"></script>
  <% } %>
  
  <!-- 非关键脚本 - 异步加载 -->
  <% for (const script of headScripts.filter(s => !s.critical)) { %>
  <script async src="<%= script.src %>"></script>
  <% } %>
</body>
</html>
