!(function(){var et=Object.defineProperty;var Qe=Object.getOwnPropertySymbols;var tt=Object.prototype.hasOwnProperty,nt=Object.prototype.propertyIsEnumerable;var Xe=(be,Q,M)=>Q in be?et(be,Q,{enumerable:!0,configurable:!0,writable:!0,value:M}):be[Q]=M,Ke=(be,Q)=>{for(var M in Q||(Q={}))tt.call(Q,M)&&Xe(be,M,Q[M]);if(Qe)for(var M of Qe(Q))nt.call(Q,M)&&Xe(be,M,Q[M]);return be};var Ge=(be,Q,M)=>Xe(be,typeof Q!="symbol"?Q+"":Q,M);(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[646],{76350:function(be,Q,M){"use strict";M.d(Q,{Il:function(){return j},Ov:function(){return W},T$:function(){return A}});var ee=M(18261),F=M(44194),K=M(45516),P=["element"],j=F.createContext({});function W(){return F.useContext(j)}function N(){var w=(0,K.TH)(),o=W(),m=o.clientRoutes,p=(0,K.fp)(m,w.pathname);return p||[]}function A(){var w,o=N().slice(-1),m=((w=o[0])===null||w===void 0?void 0:w.route)||{},p=m.element,b=(0,ee.Z)(m,P);return b}function _(){var w=N(),o=W(),m=o.serverLoaderData,p=o.basename,b=React.useState(function(){var v={},c=!1;return w.forEach(function(i){var f=m[i.route.id];f&&(Object.assign(v,f),c=!0)}),c?v:void 0}),d=_slicedToArray(b,2),k=d[0],T=d[1];return React.useEffect(function(){window.__UMI_LOADER_DATA__||Promise.all(w.filter(function(v){return v.route.hasServerLoader}).map(function(v){return new Promise(function(c){fetchServerLoader({id:v.route.id,basename:p,cb:c})})})).then(function(v){if(v.length){var c={};v.forEach(function(i){Object.assign(c,i)}),T(c)}})},[]),{data:k}}function O(){var w=useRouteData(),o=W();return{data:o.clientLoaderData[w.route.id]}}function g(){var w=_(),o=O();return{data:_objectSpread(_objectSpread({},w.data),o.data)}}},2714:function(be,Q,M){"use strict";M.d(Q,{p6:function(){return oe}});var ee=M(18260),F=M(42549),K=M(8658),P=M(44194),j=M(8082),W=M(45516),N=M(76350);function A(B){var X=B.id,se=B.basename,le=B.cb,pe=new URLSearchParams({route:X,url:window.location.href}).toString(),me="".concat(_(window.umiServerLoaderPath||se),"__serverLoader?").concat(pe);fetch(me,{credentials:"include"}).then(function(ge){return ge.json()}).then(le).catch(console.error)}function _(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return B.endsWith("/")?B:"".concat(B,"/")}var O=M(18261),g=M(16076),w=M(49338),o=["content"],m=["content"],p=/^(http:|https:)?\/\//;function b(B){return p.test(B)||B.startsWith("/")&&!B.startsWith("/*")||B.startsWith("./")||B.startsWith("../")}var d=function(){return P.createElement("noscript",{dangerouslySetInnerHTML:{__html:"<b>Enable JavaScript to run this app.</b>"}})},k=function(X){var se,le=X.loaderData,pe=X.htmlPageOpts,me=X.manifest,ge=(me==null||(se=me.assets)===null||se===void 0?void 0:se["umi.css"])||"";return P.createElement("script",{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:"window.__UMI_LOADER_DATA__ = ".concat(JSON.stringify(le||{}),"; window.__UMI_METADATA_LOADER_DATA__ = ").concat(JSON.stringify(pe||{}),"; window.__UMI_BUILD_ClIENT_CSS__ = '").concat(ge,"'")}})};function T(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(typeof B=="string")return b(B)?(0,F.Z)({src:B},X):{content:B};if((0,w.Z)(B)==="object")return(0,F.Z)((0,F.Z)({},B),X);throw new Error("Invalid script type: ".concat((0,w.Z)(B)))}function v(B){return b(B)?{type:"link",href:B}:{type:"style",content:B}}var c=function(X){var se,le,pe,me,ge,Ee,_e=X.htmlPageOpts;return P.createElement(P.Fragment,null,(_e==null?void 0:_e.title)&&P.createElement("title",null,_e.title),_e==null||(se=_e.favicons)===null||se===void 0?void 0:se.map(function(Ce,Se){return P.createElement("link",{key:Se,rel:"shortcut icon",href:Ce})}),(_e==null?void 0:_e.description)&&P.createElement("meta",{name:"description",content:_e.description}),(_e==null||(le=_e.keywords)===null||le===void 0?void 0:le.length)&&P.createElement("meta",{name:"keywords",content:_e.keywords.join(",")}),_e==null||(pe=_e.metas)===null||pe===void 0?void 0:pe.map(function(Ce){return P.createElement("meta",{key:Ce.name,name:Ce.name,content:Ce.content})}),_e==null||(me=_e.links)===null||me===void 0?void 0:me.map(function(Ce,Se){return P.createElement("link",(0,g.Z)({key:Se},Ce))}),_e==null||(ge=_e.styles)===null||ge===void 0?void 0:ge.map(function(Ce,Se){var Ie=v(Ce),Le=Ie.type,Me=Ie.href,Pe=Ie.content;if(Le==="link")return P.createElement("link",{key:Se,rel:"stylesheet",href:Me});if(Le==="style")return P.createElement("style",{key:Se},Pe)}),_e==null||(Ee=_e.headScripts)===null||Ee===void 0?void 0:Ee.map(function(Ce,Se){var Ie=T(Ce),Le=Ie.content,Me=(0,O.Z)(Ie,o);return P.createElement("script",(0,g.Z)({dangerouslySetInnerHTML:{__html:Le},key:Se},Me))}))};function i(B){var X,se=B.children,le=B.loaderData,pe=B.manifest,me=B.htmlPageOpts,ge=B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ee=B.mountElementId;if(ge!=null&&ge.pureHtml)return P.createElement("html",null,P.createElement("head",null),P.createElement("body",null,P.createElement(d,null),P.createElement("div",{id:Ee},se),P.createElement(k,{manifest:pe,loaderData:le,htmlPageOpts:me})));if(ge!=null&&ge.pureApp)return P.createElement(P.Fragment,null,se);var _e=typeof window=="undefined"?pe==null?void 0:pe.assets["umi.css"]:window.__UMI_BUILD_ClIENT_CSS__;return P.createElement("html",{suppressHydrationWarning:!0,lang:(me==null?void 0:me.lang)||"en"},P.createElement("head",null,P.createElement("meta",{charSet:"utf-8"}),P.createElement("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),_e&&P.createElement("link",{suppressHydrationWarning:!0,rel:"stylesheet",href:_e}),P.createElement(c,{htmlPageOpts:me})),P.createElement("body",null,P.createElement(d,null),P.createElement("div",{id:Ee},se),P.createElement(k,{manifest:pe,loaderData:le,htmlPageOpts:me}),me==null||(X=me.scripts)===null||X===void 0?void 0:X.map(function(Ce,Se){var Ie=T(Ce),Le=Ie.content,Me=(0,O.Z)(Ie,m);return P.createElement("script",(0,g.Z)({dangerouslySetInnerHTML:{__html:Le},key:Se},Me))})))}var f=P.createContext(void 0);function C(){return P.useContext(f)}var I=["redirect"];function D(B){var X=B.routesById,se=B.parentId,le=B.routeComponents,pe=B.useStream,me=pe===void 0?!0:pe;return Object.keys(X).filter(function(ge){return X[ge].parentId===se}).map(function(ge){var Ee=U((0,F.Z)((0,F.Z)({route:X[ge],routeComponent:le[ge],loadingComponent:B.loadingComponent,reactRouter5Compat:B.reactRouter5Compat},B.reactRouter5Compat&&{hasChildren:Object.keys(X).filter(function(Ce){return X[Ce].parentId===ge}).length>0}),{},{useStream:me})),_e=D({routesById:X,routeComponents:le,parentId:Ee.id,loadingComponent:B.loadingComponent,reactRouter5Compat:B.reactRouter5Compat,useStream:me});return _e.length>0&&(Ee.children=_e,Ee.routes=_e),Ee})}function G(B){var X=(0,W.UO)(),se=(0,W.Gn)(B.to,X),le=(0,N.T$)(),pe=(0,W.TH)();if(le!=null&&le.keepQuery){var me=pe.search+pe.hash;se+=me}var ge=(0,F.Z)((0,F.Z)({},B),{},{to:se});return P.createElement(W.Fg,(0,g.Z)({replace:!0},ge))}function U(B){var X=B.route,se=B.useStream,le=se===void 0?!0:se,pe=X.redirect,me=(0,O.Z)(X,I),ge=B.reactRouter5Compat?H:ae;return(0,F.Z)({element:pe?P.createElement(G,{to:pe}):P.createElement(f.Provider,{value:{route:B.route}},P.createElement(ge,{loader:P.memo(B.routeComponent),loadingComponent:B.loadingComponent||z,hasChildren:B.hasChildren,useStream:le}))},me)}function z(){return P.createElement("div",null)}function H(B){var X=C(),se=X.route,le=(0,N.Ov)(),pe=le.history,me=le.clientRoutes,ge=(0,W.UO)(),Ee={params:ge,isExact:!0,path:se.path,url:pe.location.pathname},_e=B.loader,Ce={location:pe.location,match:Ee,history:pe,params:ge,route:se,routes:me};return B.useStream?P.createElement(P.Suspense,{fallback:P.createElement(B.loadingComponent,null)},P.createElement(_e,Ce,B.hasChildren&&P.createElement(W.j3,null))):P.createElement(_e,Ce,B.hasChildren&&P.createElement(W.j3,null))}function ae(B){var X=B.loader;return B.useStream?P.createElement(P.Suspense,{fallback:P.createElement(B.loadingComponent,null)},P.createElement(X,null)):P.createElement(X,null)}var q=null;function L(){return q}function J(B){var X=B.history,se=P.useState({action:X.action,location:X.location}),le=(0,K.Z)(se,2),pe=le[0],me=le[1];return(0,P.useLayoutEffect)(function(){return X.listen(me)},[X]),(0,P.useLayoutEffect)(function(){function ge(Ee){B.pluginManager.applyPlugins({key:"onRouteChange",type:"event",args:{routes:B.routes,clientRoutes:B.clientRoutes,location:Ee.location,action:Ee.action,basename:B.basename,isFirst:!!Ee.isFirst}})}return ge({location:pe.location,action:pe.action,isFirst:!0}),X.listen(ge)},[X,B.routes,B.clientRoutes]),P.createElement(W.F0,{navigator:X,location:pe.location,basename:B.basename},B.children)}function te(){var B=(0,N.Ov)(),X=B.clientRoutes;return(0,W.V$)(X)}var ye=["innerProvider","i18nProvider","accessProvider","dataflowProvider","outerProvider","rootContainer"],fe=function(X,se){var le=X.basename||"/",pe=D({routesById:X.routes,routeComponents:X.routeComponents,loadingComponent:X.loadingComponent,reactRouter5Compat:X.reactRouter5Compat,useStream:X.useStream});X.pluginManager.applyPlugins({key:"patchClientRoutes",type:"event",args:{routes:pe}});for(var me=P.createElement(J,{basename:le,pluginManager:X.pluginManager,routes:X.routes,clientRoutes:pe,history:X.history},se),ge=0,Ee=ye;ge<Ee.length;ge++){var _e=Ee[ge];me=X.pluginManager.applyPlugins({type:"modify",key:_e,initialValue:me,args:{routes:X.routes,history:X.history,plugin:X.pluginManager}})}var Ce=function(){var Ie=(0,P.useState)({}),Le=(0,K.Z)(Ie,2),Me=Le[0],Pe=Le[1],Ze=(0,P.useState)(window.__UMI_LOADER_DATA__||{}),Re=(0,K.Z)(Ze,2),We=Re[0],$e=Re[1],je=(0,P.useCallback)(function(qe,He){var Be,Ne=(((Be=(0,W.fp)(pe,qe,le))===null||Be===void 0?void 0:Be.map(function(De){return De.route.id}))||[]).filter(Boolean);Ne.forEach(function(De){var ze,Ve;if(window.__umi_route_prefetch__){var u,S=(u=X.routeComponents[De])===null||u===void 0||(u=u._payload)===null||u===void 0?void 0:u._result;typeof S=="function"&&S()}var E=(ze=X.routes[De])===null||ze===void 0?void 0:ze.clientLoader,R=!!E,Z=(Ve=X.routes[De])===null||Ve===void 0?void 0:Ve.hasServerLoader;!He&&Z&&!R&&!window.__UMI_LOADER_DATA__&&A({id:De,basename:le,cb:function(we){P.startTransition(function(){$e(function(Oe){return(0,F.Z)((0,F.Z)({},Oe),{},(0,ee.Z)({},De,we))})})}});var re=!!Me[De],ce=R&&E.hydrate||!Z,ue=Z&&!window.__UMI_LOADER_DATA__;R&&!re&&(ce||ue)&&E({serverLoader:function(){return A({id:De,basename:le,cb:function(Oe){P.startTransition(function(){$e(function(Ue){return(0,F.Z)((0,F.Z)({},Ue),{},(0,ee.Z)({},De,Oe))})})}})}}).then(function(he){Pe(function(we){return(0,F.Z)((0,F.Z)({},we),{},(0,ee.Z)({},De,he))})})})},[Me]);return(0,P.useEffect)(function(){return je(window.location.pathname,!0),X.history.listen(function(qe){je(qe.location.pathname)})},[]),(0,P.useLayoutEffect)(function(){typeof X.callback=="function"&&X.callback()},[]),P.createElement(N.Il.Provider,{value:{routes:X.routes,routeComponents:X.routeComponents,clientRoutes:pe,pluginManager:X.pluginManager,rootElement:X.rootElement,basename:le,clientLoaderData:Me,serverLoaderData:We,preloadRoute:je,history:X.history}},me)};return Ce};function oe(B){var X=B.rootElement||document.getElementById("root"),se=fe(B,P.createElement(te,null));if(B.components)return se;if(B.hydrate){var le=window.__UMI_LOADER_DATA__||{},pe=window.__UMI_METADATA_LOADER_DATA__||{},me={metadata:pe,loaderData:le,mountElementId:B.mountElementId},ge=B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.pureApp||B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.pureHtml;j.hydrateRoot(ge?X:document,ge?P.createElement(se,null):P.createElement(i,me,P.createElement(se,null)));return}if(j.createRoot){q=j.createRoot(X),q.render(P.createElement(se,null));return}j.render(P.createElement(se,null),X)}},53269:function(be,Q,M){"use strict";M.d(Q,{Z:function(){return O}});var ee=M(16076),F=M(18261),K=M(44194),P=M(32696),j=M(76350),W=M(8658);function N(g,w){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},m=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};if(typeof IntersectionObserver!="function")return null;var p=K.useRef(typeof IntersectionObserver=="function"),b=K.useRef(null);return K.useEffect(function(){if(!(!g.current||!p.current||m.disabled))return b.current=new IntersectionObserver(function(d){var k=(0,W.Z)(d,1),T=k[0];w(T)},o),b.current.observe(g.current),function(){var d;(d=b.current)===null||d===void 0||d.disconnect()}},[w,o,m.disabled,g]),b.current}var A=["prefetch"];function _(g){var w=K.useRef(null);return K.useEffect(function(){g&&(typeof g=="function"?g(w.current):g.current=w.current)}),w}var O=K.forwardRef(function(g,w){var o,m=g.prefetch,p=(0,F.Z)(g,A),b=typeof window!="undefined"&&window.__umi_route_prefetch__||{defaultPrefetch:"none",defaultPrefetchTimeout:50},d=b.defaultPrefetch,k=b.defaultPrefetchTimeout,T=(m===!0?"intent":m===!1?"none":m)||d;if(!["intent","render","viewport","none"].includes(T))throw new Error("Invalid prefetch value ".concat(T," found in Link component"));var v=(0,j.Ov)(),c=typeof g.to=="string"?g.to:(o=g.to)===null||o===void 0?void 0:o.pathname,i=K.useRef(!1),f=_(w),C=function(G){if(T==="intent"){var U=G.target||{};U.preloadTimeout||(U.preloadTimeout=setTimeout(function(){var z;U.preloadTimeout=null,(z=v.preloadRoute)===null||z===void 0||z.call(v,c)},g.prefetchTimeout||k))}},I=function(G){if(T==="intent"){var U=G.target||{};U.preloadTimeout&&(clearTimeout(U.preloadTimeout),U.preloadTimeout=null)}};return(0,K.useLayoutEffect)(function(){if(T==="render"&&!i.current){var D;(D=v.preloadRoute)===null||D===void 0||D.call(v,c),i.current=!0}},[T,c]),N(f,function(D){if(D!=null&&D.isIntersecting){var G;(G=v.preloadRoute)===null||G===void 0||G.call(v,c)}},{rootMargin:"100px"},{disabled:T!=="viewport"}),c?K.createElement(P.rU,(0,ee.Z)({onMouseEnter:C,onMouseLeave:I,ref:f},p),g.children):null})},96953:function(be,Q,M){"use strict";M.d(Q,{nG:function(){return Ue},Un:function(){return R}});var ee=M(41029);function F(l,h){return h>>>l|h<<32-l}function K(l,h,y){return l&h^~l&y}function P(l,h,y){return l&h^l&y^h&y}function j(l){return F(2,l)^F(13,l)^F(22,l)}function W(l){return F(6,l)^F(11,l)^F(25,l)}function N(l){return F(7,l)^F(18,l)^l>>>3}function A(l){return F(17,l)^F(19,l)^l>>>10}function _(l,h){return l[h&15]+=A(l[h+14&15])+l[h+9&15]+N(l[h+1&15])}var O=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],g,w,o,m="0123456789abcdef";function p(l,h){var y=(l&65535)+(h&65535),$=(l>>16)+(h>>16)+(y>>16);return $<<16|y&65535}function b(){g=new Array(8),w=new Array(2),o=new Array(64),w[0]=w[1]=0,g[0]=1779033703,g[1]=3144134277,g[2]=1013904242,g[3]=2773480762,g[4]=1359893119,g[5]=2600822924,g[6]=528734635,g[7]=1541459225}function d(){var l,h,y,$,Y,ie,ne,s,t,r,a=new Array(16);l=g[0],h=g[1],y=g[2],$=g[3],Y=g[4],ie=g[5],ne=g[6],s=g[7];for(var n=0;n<16;n++)a[n]=o[(n<<2)+3]|o[(n<<2)+2]<<8|o[(n<<2)+1]<<16|o[n<<2]<<24;for(var e=0;e<64;e++)t=s+W(Y)+K(Y,ie,ne)+O[e],e<16?t+=a[e]:t+=_(a,e),r=j(l)+P(l,h,y),s=ne,ne=ie,ie=Y,Y=p($,t),$=y,y=h,h=l,l=p(t,r);g[0]+=l,g[1]+=h,g[2]+=y,g[3]+=$,g[4]+=Y,g[5]+=ie,g[6]+=ne,g[7]+=s}function k(l,h){var y,$,Y=0;$=w[0]>>3&63;var ie=h&63;for((w[0]+=h<<3)<h<<3&&w[1]++,w[1]+=h>>29,y=0;y+63<h;y+=64){for(var ne=$;ne<64;ne++)o[ne]=l.charCodeAt(Y++);d(),$=0}for(var s=0;s<ie;s++)o[s]=l.charCodeAt(Y++)}function T(){var l=w[0]>>3&63;if(o[l++]=128,l<=56)for(var h=l;h<56;h++)o[h]=0;else{for(var y=l;y<64;y++)o[y]=0;d();for(var $=0;$<56;$++)o[$]=0}o[56]=w[1]>>>24&255,o[57]=w[1]>>>16&255,o[58]=w[1]>>>8&255,o[59]=w[1]&255,o[60]=w[0]>>>24&255,o[61]=w[0]>>>16&255,o[62]=w[0]>>>8&255,o[63]=w[0]&255,d()}function v(){for(var l=0,h=new Array(32),y=0;y<8;y++)h[l++]=g[y]>>>24&255,h[l++]=g[y]>>>16&255,h[l++]=g[y]>>>8&255,h[l++]=g[y]&255;return h}function c(){for(var l=new String,h=0;h<8;h++)for(var y=28;y>=0;y-=4)l+=m.charAt(g[h]>>>y&15);return l}function i(l){return b(),k(l,l.length),T(),c()}var f=i;function C(l){"@babel/helpers - typeof";return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(h){return typeof h}:function(h){return h&&typeof Symbol=="function"&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},C(l)}var I=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function D(l,h){return z(l)||U(l,h)||Ee(l,h)||G()}function G(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function U(l,h){var y=l==null?null:typeof Symbol!="undefined"&&l[Symbol.iterator]||l["@@iterator"];if(y!=null){var $=[],Y=!0,ie=!1,ne,s;try{for(y=y.call(l);!(Y=(ne=y.next()).done)&&($.push(ne.value),!(h&&$.length===h));Y=!0);}catch(t){ie=!0,s=t}finally{try{!Y&&y.return!=null&&y.return()}finally{if(ie)throw s}}return $}}function z(l){if(Array.isArray(l))return l}function H(l,h){var y=typeof Symbol!="undefined"&&l[Symbol.iterator]||l["@@iterator"];if(!y){if(Array.isArray(l)||(y=Ee(l))||h&&l&&typeof l.length=="number"){y&&(l=y);var $=0,Y=function(){};return{s:Y,n:function(){return $>=l.length?{done:!0}:{done:!1,value:l[$++]}},e:function(r){throw r},f:Y}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ie=!0,ne=!1,s;return{s:function(){y=y.call(l)},n:function(){var r=y.next();return ie=r.done,r},e:function(r){ne=!0,s=r},f:function(){try{!ie&&y.return!=null&&y.return()}finally{if(ne)throw s}}}}function ae(l,h){if(!(l instanceof h))throw new TypeError("Cannot call a class as a function")}function q(l,h){for(var y=0;y<h.length;y++){var $=h[y];$.enumerable=$.enumerable||!1,$.configurable=!0,"value"in $&&($.writable=!0),Object.defineProperty(l,$.key,$)}}function L(l,h,y){return h&&q(l.prototype,h),y&&q(l,y),Object.defineProperty(l,"prototype",{writable:!1}),l}function J(l,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(h&&h.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),Object.defineProperty(l,"prototype",{writable:!1}),h&&le(l,h)}function te(l){var h=X();return function(){var $=pe(l),Y;if(h){var ie=pe(this).constructor;Y=Reflect.construct($,arguments,ie)}else Y=$.apply(this,arguments);return ye(this,Y)}}function ye(l,h){if(h&&(C(h)==="object"||typeof h=="function"))return h;if(h!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fe(l)}function fe(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function oe(l){var h=typeof Map=="function"?new Map:void 0;return oe=function($){if($===null||!se($))return $;if(typeof $!="function")throw new TypeError("Super expression must either be null or a function");if(typeof h!="undefined"){if(h.has($))return h.get($);h.set($,Y)}function Y(){return B($,arguments,pe(this).constructor)}return Y.prototype=Object.create($.prototype,{constructor:{value:Y,enumerable:!1,writable:!0,configurable:!0}}),le(Y,$)},oe(l)}function B(l,h,y){return X()?B=Reflect.construct.bind():B=function(Y,ie,ne){var s=[null];s.push.apply(s,ie);var t=Function.bind.apply(Y,s),r=new t;return ne&&le(r,ne.prototype),r},B.apply(null,arguments)}function X(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(l){return!1}}function se(l){return Function.toString.call(l).indexOf("[native code]")!==-1}function le(l,h){return le=Object.setPrototypeOf?Object.setPrototypeOf.bind():function($,Y){return $.__proto__=Y,$},le(l,h)}function pe(l){return pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(y){return y.__proto__||Object.getPrototypeOf(y)},pe(l)}function me(l){return Ce(l)||_e(l)||Ee(l)||ge()}function ge(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ee(l,h){if(l){if(typeof l=="string")return Se(l,h);var y=Object.prototype.toString.call(l).slice(8,-1);if(y==="Object"&&l.constructor&&(y=l.constructor.name),y==="Map"||y==="Set")return Array.from(l);if(y==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(y))return Se(l,h)}}function _e(l){if(typeof Symbol!="undefined"&&l[Symbol.iterator]!=null||l["@@iterator"]!=null)return Array.from(l)}function Ce(l){if(Array.isArray(l))return Se(l)}function Se(l,h){(h==null||h>l.length)&&(h=l.length);for(var y=0,$=new Array(h);y<h;y++)$[y]=l[y];return $}function Ie(l,h){if(l==null)return{};var y=Le(l,h),$,Y;if(Object.getOwnPropertySymbols){var ie=Object.getOwnPropertySymbols(l);for(Y=0;Y<ie.length;Y++)$=ie[Y],!(h.indexOf($)>=0)&&Object.prototype.propertyIsEnumerable.call(l,$)&&(y[$]=l[$])}return y}function Le(l,h){if(l==null)return{};var y={},$=Object.keys(l),Y,ie;for(ie=0;ie<$.length;ie++)Y=$[ie],!(h.indexOf(Y)>=0)&&(y[Y]=l[Y]);return y}function Me(l,h){var y=Object.keys(l);if(Object.getOwnPropertySymbols){var $=Object.getOwnPropertySymbols(l);h&&($=$.filter(function(Y){return Object.getOwnPropertyDescriptor(l,Y).enumerable})),y.push.apply(y,$)}return y}function Pe(l){for(var h=1;h<arguments.length;h++){var y=arguments[h]!=null?arguments[h]:{};h%2?Me(Object(y),!0).forEach(function($){Ze(l,$,y[$])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(y)):Me(Object(y)).forEach(function($){Object.defineProperty(l,$,Object.getOwnPropertyDescriptor(y,$))})}return l}function Ze(l,h,y){return h in l?Object.defineProperty(l,h,{value:y,enumerable:!0,configurable:!0,writable:!0}):l[h]=y,l}var Re="routes";function We(l){return l.split("?")[0].split("#")[0]}var $e=function(h){if(!h.startsWith("http"))return!1;try{var y=new URL(h);return!!y}catch($){return!1}},je=function(h){var y=h.path;if(!y||y==="/")try{return"/".concat(f(JSON.stringify(h)))}catch($){}return y&&We(y)},qe=function(h,y){var $=h.name,Y=h.locale;return"locale"in h&&Y===!1||!$?!1:h.locale||"".concat(y,".").concat($)},He=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return h.endsWith("/*")?h.replace("/*","/"):(h||y).startsWith("/")||$e(h)?h:"/".concat(y,"/").concat(h).replace(/\/\//g,"/").replace(/\/\//g,"/")},Be=function(h,y){var $=h.menu,Y=$===void 0?{}:$,ie=h.indexRoute,ne=h.path,s=ne===void 0?"":ne,t=h.children||[],r=Y.name,a=r===void 0?h.name:r,n=Y.icon,e=n===void 0?h.icon:n,x=Y.hideChildren,V=x===void 0?h.hideChildren:x,de=Y.flatMenu,xe=de===void 0?h.flatMenu:de,Ae=ie&&Object.keys(ie).join(",")!=="redirect"?[Pe({path:s,menu:Y},ie)].concat(t||[]):t,ke=Pe({},h);if(a&&(ke.name=a),e&&(ke.icon=e),Ae&&Ae.length){if(V)return delete ke.children,ke;var Te=De(Pe(Pe({},y),{},{data:Ae}),h);if(xe)return Te;delete ke[Re]}return ke},Ne=function(h){return Array.isArray(h)&&h.length>0};function De(l){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},y=l.data,$=l.formatMessage,Y=l.parentName,ie=l.locale;return!y||!Array.isArray(y)?[]:y.filter(function(ne){return ne?Ne(ne.children)||ne.path||ne.originPath||ne.layout?!0:(ne.redirect||ne.unaccessible,!1):!1}).filter(function(ne){var s,t;return!(ne==null||(s=ne.menu)===null||s===void 0)&&s.name||ne!=null&&ne.flatMenu||!(ne==null||(t=ne.menu)===null||t===void 0)&&t.flatMenu?!0:ne.menu!==!1}).map(function(ne){var s=Pe(Pe({},ne),{},{path:ne.path||ne.originPath});return!s.children&&s[Re]&&(s.children=s[Re],delete s[Re]),s.unaccessible&&delete s.name,s.path==="*"&&(s.path="."),s.path==="/*"&&(s.path="."),!s.path&&s.originPath&&(s.path=s.originPath),s}).map(function(){var ne=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},s=ne.children||ne[Re]||[],t=He(ne.path,h?h.path:"/"),r=ne.name,a=qe(ne,Y||"menu"),n=a!==!1&&ie!==!1&&$&&a?$({id:a,defaultMessage:r}):r,e=h.pro_layout_parentKeys,x=e===void 0?[]:e,V=h.children,de=h.icon,xe=h.flatMenu,Ae=h.indexRoute,ke=h.routes,Te=Ie(h,I),Fe=new Set([].concat(me(x),me(ne.parentKeys||[])));h.key&&Fe.add(h.key);var ve=Pe(Pe(Pe({},Te),{},{menu:void 0},ne),{},{path:t,locale:a,key:ne.key||je(Pe(Pe({},ne),{},{path:t})),pro_layout_parentKeys:Array.from(Fe).filter(function(Ye){return Ye&&Ye!=="/"})});if(n?ve.name=n:delete ve.name,ve.menu===void 0&&delete ve.menu,Ne(s)){var Je=De(Pe(Pe({},l),{},{data:s,parentName:a||""}),ve);Ne(Je)&&(ve.children=Je)}return Be(ve,l)}).flat(1)}var ze=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return h.filter(function(y){return y&&(y.name||Ne(y.children))&&!y.hideInMenu&&!y.redirect}).map(function(y){var $=Pe({},y),Y=$.children||y[Re]||[];if(delete $[Re],Ne(Y)&&!$.hideChildrenInMenu&&Y.some(function(ne){return ne&&!!ne.name})){var ie=l(Y);if(ie.length)return Pe(Pe({},$),{},{children:ie})}return Pe({},y)}).filter(function(y){return y})},Ve=function(l){J(y,l);var h=te(y);function y(){return ae(this,y),h.apply(this,arguments)}return L(y,[{key:"get",value:function(Y){var ie;try{var ne=H(this.entries()),s;try{for(ne.s();!(s=ne.n()).done;){var t=D(s.value,2),r=t[0],a=t[1],n=We(r);if(!$e(r)&&(0,ee.Bo)(n,[]).test(Y)){ie=a;break}}}catch(e){ne.e(e)}finally{ne.f()}}catch(e){ie=void 0}return ie}}]),y}(oe(Map)),u=function(h){var y=new Ve,$=function Y(ie,ne){ie.forEach(function(s){var t=s.children||s[Re]||[];Ne(t)&&Y(t,s);var r=He(s.path,ne?ne.path:"/");y.set(We(r),s)})};return $(h),y},S=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return h.map(function(y){var $=y.children||y[Re];if(Ne($)){var Y=l($);if(Y.length)return Pe({},y)}var ie=Pe({},y);return delete ie[Re],delete ie.children,ie}).filter(function(y){return y})},E=function(h,y,$,Y){var ie=De({data:h,formatMessage:$,locale:y}),ne=Y?S(ie):ze(ie),s=u(ie);return{breadcrumb:s,menuData:ne}},R=E;function Z(l,h){var y=Object.keys(l);if(Object.getOwnPropertySymbols){var $=Object.getOwnPropertySymbols(l);h&&($=$.filter(function(Y){return Object.getOwnPropertyDescriptor(l,Y).enumerable})),y.push.apply(y,$)}return y}function re(l){for(var h=1;h<arguments.length;h++){var y=arguments[h]!=null?arguments[h]:{};h%2?Z(Object(y),!0).forEach(function($){ce(l,$,y[$])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(y)):Z(Object(y)).forEach(function($){Object.defineProperty(l,$,Object.getOwnPropertyDescriptor(y,$))})}return l}function ce(l,h,y){return h in l?Object.defineProperty(l,h,{value:y,enumerable:!0,configurable:!0,writable:!0}):l[h]=y,l}var ue=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],y={};return h.forEach(function($){var Y=re({},$);if(!(!Y||!Y.key)){!Y.children&&Y[Re]&&(Y.children=Y[Re],delete Y[Re]);var ie=Y.children||[];y[We(Y.path||Y.key||"/")]=re({},Y),y[Y.key||Y.path||"/"]=re({},Y),ie&&(y=re(re({},y),l(ie)))}}),y},he=ue,we=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],y=arguments.length>1?arguments[1]:void 0,$=arguments.length>2?arguments[2]:void 0;return h.filter(function(Y){if(Y==="/"&&y==="/")return!0;if(Y!=="/"&&Y!=="/*"&&Y&&!$e(Y)){var ie=We(Y);try{if($&&(0,ee.Bo)("".concat(ie)).test(y)||(0,ee.Bo)("".concat(ie),[]).test(y)||(0,ee.Bo)("".concat(ie,"/(.*)")).test(y))return!0}catch(ne){}}return!1}).sort(function(Y,ie){return Y===y?10:ie===y?-10:Y.substr(1).split("/").length-ie.substr(1).split("/").length})},Oe=function(h,y,$,Y){var ie=he(y),ne=Object.keys(ie),s=we(ne,h||"/",Y);return!s||s.length<1?[]:($||(s=[s[s.length-1]]),s.map(function(t){var r=ie[t]||{pro_layout_parentKeys:"",key:""},a=new Map,n=(r.pro_layout_parentKeys||[]).map(function(e){return a.has(e)?null:(a.set(e,!0),ie[e])}).filter(function(e){return e});return r.key&&n.push(r),n}).flat(1))},Ue=Oe},41029:function(be,Q){var M;function ee(p){"@babel/helpers - typeof";return ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},ee(p)}M={value:!0},Q.Bo=M=M=M=M=M=M=void 0;function F(p){for(var b=[],d=0;d<p.length;){var k=p[d];if(k==="*"||k==="+"||k==="?"){b.push({type:"MODIFIER",index:d,value:p[d++]});continue}if(k==="\\"){b.push({type:"ESCAPED_CHAR",index:d++,value:p[d++]});continue}if(k==="{"){b.push({type:"OPEN",index:d,value:p[d++]});continue}if(k==="}"){b.push({type:"CLOSE",index:d,value:p[d++]});continue}if(k===":"){for(var T="",v=d+1;v<p.length;){var c=p.charCodeAt(v);if(c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||c===95){T+=p[v++];continue}break}if(!T)throw new TypeError("Missing parameter name at "+d);b.push({type:"NAME",index:d,value:T}),d=v;continue}if(k==="("){var i=1,f="",v=d+1;if(p[v]==="?")throw new TypeError('Pattern cannot start with "?" at '+v);for(;v<p.length;){if(p[v]==="\\"){f+=p[v++]+p[v++];continue}if(p[v]===")"){if(i--,i===0){v++;break}}else if(p[v]==="("&&(i++,p[v+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+v);f+=p[v++]}if(i)throw new TypeError("Unbalanced pattern at "+d);if(!f)throw new TypeError("Missing pattern at "+d);b.push({type:"PATTERN",index:d,value:f}),d=v;continue}b.push({type:"CHAR",index:d,value:p[d++]})}return b.push({type:"END",index:d,value:""}),b}function K(p,b){b===void 0&&(b={});for(var d=F(p),k=b.prefixes,T=k===void 0?"./":k,v="[^"+A(b.delimiter||"/#?")+"]+?",c=[],i=0,f=0,C="",I=function(oe){if(f<d.length&&d[f].type===oe)return d[f++].value},D=function(oe){var B=I(oe);if(B!==void 0)return B;var X=d[f],se=X.type,le=X.index;throw new TypeError("Unexpected "+se+" at "+le+", expected "+oe)},G=function(){for(var oe="",B;B=I("CHAR")||I("ESCAPED_CHAR");)oe+=B;return oe};f<d.length;){var U=I("CHAR"),z=I("NAME"),H=I("PATTERN");if(z||H){var ae=U||"";T.indexOf(ae)===-1&&(C+=ae,ae=""),C&&(c.push(C),C=""),c.push({name:z||i++,prefix:ae,suffix:"",pattern:H||v,modifier:I("MODIFIER")||""});continue}var q=U||I("ESCAPED_CHAR");if(q){C+=q;continue}C&&(c.push(C),C="");var L=I("OPEN");if(L){var ae=G(),J=I("NAME")||"",te=I("PATTERN")||"",ye=G();D("CLOSE"),c.push({name:J||(te?i++:""),pattern:J&&!te?v:te,prefix:ae,suffix:ye,modifier:I("MODIFIER")||""});continue}D("END")}return c}M=K;function P(p,b){return j(K(p,b),b)}M=P;function j(p,b){b===void 0&&(b={});var d=_(b),k=b.encode,T=k===void 0?function(f){return f}:k,v=b.validate,c=v===void 0?!0:v,i=p.map(function(f){if(ee(f)==="object")return new RegExp("^(?:"+f.pattern+")$",d)});return function(f){for(var C="",I=0;I<p.length;I++){var D=p[I];if(typeof D=="string"){C+=D;continue}var G=f?f[D.name]:void 0,U=D.modifier==="?"||D.modifier==="*",z=D.modifier==="*"||D.modifier==="+";if(Array.isArray(G)){if(!z)throw new TypeError('Expected "'+D.name+'" to not repeat, but got an array');if(G.length===0){if(U)continue;throw new TypeError('Expected "'+D.name+'" to not be empty')}for(var H=0;H<G.length;H++){var ae=T(G[H],D);if(c&&!i[I].test(ae))throw new TypeError('Expected all "'+D.name+'" to match "'+D.pattern+'", but got "'+ae+'"');C+=D.prefix+ae+D.suffix}continue}if(typeof G=="string"||typeof G=="number"){var ae=T(String(G),D);if(c&&!i[I].test(ae))throw new TypeError('Expected "'+D.name+'" to match "'+D.pattern+'", but got "'+ae+'"');C+=D.prefix+ae+D.suffix;continue}if(!U){var q=z?"an array":"a string";throw new TypeError('Expected "'+D.name+'" to be '+q)}}return C}}M=j;function W(p,b){var d=[],k=m(p,d,b);return N(k,d,b)}M=W;function N(p,b,d){d===void 0&&(d={});var k=d.decode,T=k===void 0?function(v){return v}:k;return function(v){var c=p.exec(v);if(!c)return!1;for(var i=c[0],f=c.index,C=Object.create(null),I=function(U){if(c[U]===void 0)return"continue";var z=b[U-1];z.modifier==="*"||z.modifier==="+"?C[z.name]=c[U].split(z.prefix+z.suffix).map(function(H){return T(H,z)}):C[z.name]=T(c[U],z)},D=1;D<c.length;D++)I(D);return{path:i,index:f,params:C}}}M=N;function A(p){return p.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function _(p){return p&&p.sensitive?"":"i"}function O(p,b){if(!b)return p;var d=p.source.match(/\((?!\?)/g);if(d)for(var k=0;k<d.length;k++)b.push({name:k,prefix:"",suffix:"",modifier:"",pattern:""});return p}function g(p,b,d){var k=p.map(function(T){return m(T,b,d).source});return new RegExp("(?:"+k.join("|")+")",_(d))}function w(p,b,d){return o(K(p,d),b,d)}function o(p,b,d){d===void 0&&(d={});for(var k=d.strict,T=k===void 0?!1:k,v=d.start,c=v===void 0?!0:v,i=d.end,f=i===void 0?!0:i,C=d.encode,I=C===void 0?function(fe){return fe}:C,D="["+A(d.endsWith||"")+"]|$",G="["+A(d.delimiter||"/#?")+"]",U=c?"^":"",z=0,H=p;z<H.length;z++){var ae=H[z];if(typeof ae=="string")U+=A(I(ae));else{var q=A(I(ae.prefix)),L=A(I(ae.suffix));if(ae.pattern)if(b&&b.push(ae),q||L)if(ae.modifier==="+"||ae.modifier==="*"){var J=ae.modifier==="*"?"?":"";U+="(?:"+q+"((?:"+ae.pattern+")(?:"+L+q+"(?:"+ae.pattern+"))*)"+L+")"+J}else U+="(?:"+q+"("+ae.pattern+")"+L+")"+ae.modifier;else U+="("+ae.pattern+")"+ae.modifier;else U+="(?:"+q+L+")"+ae.modifier}}if(f)T||(U+=G+"?"),U+=d.endsWith?"(?="+D+")":"$";else{var te=p[p.length-1],ye=typeof te=="string"?G.indexOf(te[te.length-1])>-1:te===void 0;T||(U+="(?:"+G+"(?="+D+"))?"),ye||(U+="(?="+G+"|"+D+")")}return new RegExp(U,_(d))}M=o;function m(p,b,d){return p instanceof RegExp?O(p,b):Array.isArray(p)?g(p,b,d):w(p,b,d)}Q.Bo=m},61087:function(be,Q,M){"use strict";M.d(Q,{l:function(){return P}});var ee=M(44194),F=function(){return F=Object.assign||function(N){for(var A,_=1,O=arguments.length;_<O;_++){A=arguments[_];for(var g in A)Object.prototype.hasOwnProperty.call(A,g)&&(N[g]=A[g])}return N},F.apply(this,arguments)};function K(N){var A,_=(typeof window!="undefined"?window:{}).URL,O=new _((A=window==null?void 0:window.location)===null||A===void 0?void 0:A.href);return Object.keys(N).forEach(function(g){var w=N[g];w!=null?Array.isArray(w)?(O.searchParams.delete(g),w.forEach(function(o){O.searchParams.append(g,o)})):w instanceof Date?Number.isNaN(w.getTime())||O.searchParams.set(g,w.toISOString()):typeof w=="object"?O.searchParams.set(g,JSON.stringify(w)):O.searchParams.set(g,w):O.searchParams.delete(g)}),O}function P(N,A){var _;N===void 0&&(N={}),A===void 0&&(A={disabled:!1});var O=(0,ee.useState)(),g=O[1],w=typeof window!="undefined"&&((_=window==null?void 0:window.location)===null||_===void 0?void 0:_.search),o=(0,ee.useMemo)(function(){return A.disabled?{}:new URLSearchParams(w||{})},[A.disabled,w]),m=(0,ee.useMemo)(function(){if(A.disabled)return{};if(typeof window=="undefined"||!window.URL)return{};var d=[];o.forEach(function(T,v){d.push({key:v,value:T})}),d=d.reduce(function(T,v){return(T[v.key]=T[v.key]||[]).push(v),T},{}),d=Object.keys(d).map(function(T){var v=d[T];return v.length===1?[T,v[0].value]:[T,v.map(function(c){var i=c.value;return i})]});var k=F({},N);return d.forEach(function(T){var v=T[0],c=T[1];k[v]=W(v,c,{},N)}),k},[A.disabled,N,o]);function p(d){if(!(typeof window=="undefined"||!window.URL)){var k=K(d);window.location.search!==k.search&&window.history.replaceState({},"",k.toString()),o.toString()!==k.searchParams.toString()&&g({})}}(0,ee.useEffect)(function(){A.disabled||typeof window=="undefined"||!window.URL||p(F(F({},N),m))},[A.disabled,m]);var b=function(d){p(d)};return(0,ee.useEffect)(function(){if(A.disabled)return function(){};if(typeof window=="undefined"||!window.URL)return function(){};var d=function(){g({})};return window.addEventListener("popstate",d),function(){window.removeEventListener("popstate",d)}},[A.disabled]),[m,b]}var j={true:!0,false:!1};function W(N,A,_,O){if(!_)return A;var g=_[N],w=A===void 0?O[N]:A;return g===Number?Number(w):g===Boolean||A==="true"||A==="false"?j[w]:Array.isArray(g)?g.find(function(o){return o==w})||O[N]:w}},43047:function(be,Q,M){"use strict";M.d(Q,{l7:function(){return De}});var ee=M(39137),F=M.n(ee),K=M(39060),P=M.n(K),j=M(73656);function W(u,S){var E=Object.keys(u);if(Object.getOwnPropertySymbols){var R=Object.getOwnPropertySymbols(u);S&&(R=R.filter(function(Z){return Object.getOwnPropertyDescriptor(u,Z).enumerable})),E.push.apply(E,R)}return E}function N(u){for(var S=1;S<arguments.length;S++){var E=arguments[S]!=null?arguments[S]:{};S%2?W(Object(E),!0).forEach(function(R){w(u,R,E[R])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(E)):W(Object(E)).forEach(function(R){Object.defineProperty(u,R,Object.getOwnPropertyDescriptor(E,R))})}return u}function A(u){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?A=function(S){return typeof S}:A=function(S){return S&&typeof Symbol=="function"&&S.constructor===Symbol&&S!==Symbol.prototype?"symbol":typeof S},A(u)}function _(u,S){if(!(u instanceof S))throw new TypeError("Cannot call a class as a function")}function O(u,S){for(var E=0;E<S.length;E++){var R=S[E];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(u,R.key,R)}}function g(u,S,E){return S&&O(u.prototype,S),E&&O(u,E),u}function w(u,S,E){return S in u?Object.defineProperty(u,S,{value:E,enumerable:!0,configurable:!0,writable:!0}):u[S]=E,u}function o(u,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function");u.prototype=Object.create(S&&S.prototype,{constructor:{value:u,writable:!0,configurable:!0}}),S&&p(u,S)}function m(u){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)},m(u)}function p(u,S){return p=Object.setPrototypeOf||function(R,Z){return R.__proto__=Z,R},p(u,S)}function b(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(u){return!1}}function d(u,S,E){return b()?d=Reflect.construct:d=function(Z,re,ce){var ue=[null];ue.push.apply(ue,re);var he=Function.bind.apply(Z,ue),we=new he;return ce&&p(we,ce.prototype),we},d.apply(null,arguments)}function k(u){return Function.toString.call(u).indexOf("[native code]")!==-1}function T(u){var S=typeof Map=="function"?new Map:void 0;return T=function(R){if(R===null||!k(R))return R;if(typeof R!="function")throw new TypeError("Super expression must either be null or a function");if(typeof S!="undefined"){if(S.has(R))return S.get(R);S.set(R,Z)}function Z(){return d(R,arguments,m(this).constructor)}return Z.prototype=Object.create(R.prototype,{constructor:{value:Z,enumerable:!1,writable:!0,configurable:!0}}),p(Z,R)},T(u)}function v(u){if(u===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u}function c(u,S){if(S&&(typeof S=="object"||typeof S=="function"))return S;if(S!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v(u)}function i(u){var S=b();return function(){var R=m(u),Z;if(S){var re=m(this).constructor;Z=Reflect.construct(R,arguments,re)}else Z=R.apply(this,arguments);return c(this,Z)}}function f(u){return C(u)||I(u)||D(u)||U()}function C(u){if(Array.isArray(u))return G(u)}function I(u){if(typeof Symbol!="undefined"&&u[Symbol.iterator]!=null||u["@@iterator"]!=null)return Array.from(u)}function D(u,S){if(u){if(typeof u=="string")return G(u,S);var E=Object.prototype.toString.call(u).slice(8,-1);if(E==="Object"&&u.constructor&&(E=u.constructor.name),E==="Map"||E==="Set")return Array.from(u);if(E==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(E))return G(u,S)}}function G(u,S){(S==null||S>u.length)&&(S=u.length);for(var E=0,R=new Array(S);E<S;E++)R[E]=u[E];return R}function U(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function z(u){if(!Array.isArray(u))throw new TypeError("Middlewares must be an array!");for(var S=u.length,E=0;E<S;E++)if(typeof u[E]!="function")throw new TypeError("Middleware must be componsed of function");return function(Z,re){var ce=-1;function ue(he){if(he<=ce)return Promise.reject(new Error("next() should not be called multiple times in one middleware!"));ce=he;var we=u[he]||re;if(!we)return Promise.resolve();try{return Promise.resolve(we(Z,function(){return ue(he+1)}))}catch(Oe){return Promise.reject(Oe)}}return ue(0)}}var H=function(){function u(S){if(_(this,u),!Array.isArray(S))throw new TypeError("Default middlewares must be an array!");this.defaultMiddlewares=f(S),this.middlewares=[]}return g(u,[{key:"use",value:function(E){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!1,core:!1,defaultInstance:!1},Z=!1,re=!1,ce=!1;if(typeof R=="number"?(Z=!0,re=!1):A(R)==="object"&&R&&(re=R.global||!1,Z=R.core||!1,ce=R.defaultInstance||!1),re){u.globalMiddlewares.splice(u.globalMiddlewares.length-u.defaultGlobalMiddlewaresLength,0,E);return}if(Z){u.coreMiddlewares.splice(u.coreMiddlewares.length-u.defaultCoreMiddlewaresLength,0,E);return}if(ce){this.defaultMiddlewares.push(E);return}this.middlewares.push(E)}},{key:"execute",value:function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,R=z([].concat(f(this.middlewares),f(this.defaultMiddlewares),f(u.globalMiddlewares),f(u.coreMiddlewares)));return R(E)}}]),u}();H.globalMiddlewares=[],H.defaultGlobalMiddlewaresLength=0,H.coreMiddlewares=[],H.defaultCoreMiddlewaresLength=0;var ae=function(){function u(S){_(this,u),this.cache=new Map,this.timer={},this.extendOptions(S)}return g(u,[{key:"extendOptions",value:function(E){this.maxCache=E.maxCache||0}},{key:"get",value:function(E){return this.cache.get(JSON.stringify(E))}},{key:"set",value:function(E,R){var Z=this,re=arguments.length>2&&arguments[2]!==void 0?arguments[2]:6e4;if(this.maxCache>0&&this.cache.size>=this.maxCache){var ce=f(this.cache.keys())[0];this.cache.delete(ce),this.timer[ce]&&clearTimeout(this.timer[ce])}var ue=JSON.stringify(E);this.cache.set(ue,R),re>0&&(this.timer[ue]=setTimeout(function(){Z.cache.delete(ue),delete Z.timer[ue]},re))}},{key:"delete",value:function(E){var R=JSON.stringify(E);return delete this.timer[R],this.cache.delete(R)}},{key:"clear",value:function(){return this.timer={},this.cache.clear()}}]),u}(),q=function(u){o(E,u);var S=i(E);function E(R,Z){var re,ce=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"RequestError";return _(this,E),re=S.call(this,R),re.name="RequestError",re.request=Z,re.type=ce,re}return E}(T(Error)),L=function(u){o(E,u);var S=i(E);function E(R,Z,re,ce){var ue,he=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"ResponseError";return _(this,E),ue=S.call(this,Z||R.statusText),ue.name="ResponseError",ue.data=re,ue.response=R,ue.request=ce,ue.type=he,ue}return E}(T(Error));function J(u){return new Promise(function(S,E){var R=new FileReader;R.onload=function(){S(R.result)},R.onerror=E,R.readAsText(u,"GBK")})}function te(u){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,E=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,R=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;try{return JSON.parse(u)}catch(Z){if(S)throw new L(E,"JSON.parse fail",u,R,"ParseError")}return u}function ye(u,S,E){return new Promise(function(R,Z){setTimeout(function(){Z(new q(S||"timeout of ".concat(u,"ms exceeded"),E,"Timeout"))},u)})}function fe(u){return new Promise(function(S,E){u.cancelToken&&u.cancelToken.promise.then(function(R){E(R)})})}var oe=Object.prototype.toString;function B(){var u;return typeof j!="undefined"&&oe.call(j)==="[object process]"&&(u="NODE"),typeof XMLHttpRequest!="undefined"&&(u="BROWSER"),u}function X(u){return A(u)==="object"&&Object.prototype.toString.call(u)==="[object Array]"}function se(u){return typeof URLSearchParams!="undefined"&&u instanceof URLSearchParams}function le(u){return A(u)==="object"&&Object.prototype.toString.call(u)==="[object Date]"}function pe(u){return u!==null&&A(u)==="object"}function me(u,S){if(u)if(A(u)!=="object"&&(u=[u]),X(u))for(var E=0;E<u.length;E++)S.call(null,u[E],E,u);else for(var R in u)Object.prototype.hasOwnProperty.call(u,R)&&S.call(null,u[R],R,u)}function ge(u){return se(u)?(0,ee.parse)(u.toString(),{strictNullHandling:!0}):typeof u=="string"?[u]:u}function Ee(u){return(0,ee.stringify)(u,{arrayFormat:"repeat",strictNullHandling:!0})}function _e(u,S){return N(N(N({},u),S),{},{headers:N(N({},u.headers),S.headers),params:N(N({},ge(u.params)),ge(S.params)),method:(S.method||u.method||"get").toLowerCase()})}var Ce=function(S){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},R=E.prefix,Z=E.suffix;return R&&(S="".concat(R).concat(S)),Z&&(S="".concat(S).concat(Z)),{url:S,options:E}},Se=!1;function Ie(u,S){var E=S.method,R=E===void 0?"get":E;return R.toLowerCase()==="get"}function Le(u,S){if(!u)return S();var E=u.req;E=E===void 0?{}:E;var R=E.options,Z=R===void 0?{}:R,re=E.url,ce=re===void 0?"":re,ue=u.cache,he=u.responseInterceptors,we=Z.timeout,Oe=we===void 0?0:we,Ue=Z.timeoutMessage,l=Z.__umiRequestCoreType__,h=l===void 0?"normal":l,y=Z.useCache,$=y===void 0?!1:y,Y=Z.method,ie=Y===void 0?"get":Y,ne=Z.params,s=Z.ttl,t=Z.validateCache,r=t===void 0?Ie:t;if(h!=="normal")return S();var a=fetch;if(!a)throw new Error("Global fetch not exist!");var n=B()==="BROWSER",e=r(ce,Z)&&$&&n;if(e){var x=ue.get({url:ce,params:ne,method:ie});if(x)return x=x.clone(),x.useCache=!0,u.res=x,S()}var V;return Oe>0?V=Promise.race([fe(Z),a(ce,Z),ye(Oe,Ue,u.req)]):V=Promise.race([fe(Z),a(ce,Z)]),he.forEach(function(de){V=V.then(function(xe){var Ae=typeof xe.clone=="function"?xe.clone():xe;return de(Ae,Z)})}),V.then(function(de){if(e&&de.status===200){var xe=de.clone();xe.useCache=!0,ue.set({url:ce,params:ne,method:ie},xe,s)}return u.res=de,S()})}function Me(u,S){var E;return S().then(function(){if(u){var R=u.res,Z=R===void 0?{}:R,re=u.req,ce=re===void 0?{}:re,ue=ce||{},he=ue.options;he=he===void 0?{}:he;var we=he.responseType,Oe=we===void 0?"json":we,Ue=he.charset,l=Ue===void 0?"utf8":Ue,h=he.getResponse,y=he.throwErrIfParseFail,$=y===void 0?!1:y,Y=he.parseResponse,ie=Y===void 0?!0:Y;if(ie&&!(!Z||!Z.clone)){if(E=B()==="BROWSER"?Z.clone():Z,E.useCache=Z.useCache||!1,l==="gbk")try{return Z.blob().then(J).then(function(ne){return te(ne,!1,E,ce)})}catch(ne){throw new L(E,ne.message,null,ce,"ParseError")}else if(Oe==="json")return Z.text().then(function(ne){return te(ne,$,E,ce)});try{return Z[Oe]()}catch(ne){throw new L(E,"responseType not support",null,ce,"ParseError")}}}}).then(function(R){if(u){var Z=u.res,re=u.req,ce=re===void 0?{}:re,ue=ce||{},he=ue.options;he=he===void 0?{}:he;var we=he.getResponse,Oe=we===void 0?!1:we;if(E){if(E.status>=200&&E.status<300){if(Oe){u.res={data:R,response:E};return}u.res=R;return}throw new L(E,"http error",R,ce,"HttpError")}}}).catch(function(R){if(R instanceof q||R instanceof L)throw R;var Z=u.req,re=u.res;throw R.request=R.request||Z,R.response=R.response||re,R.type=R.type||R.name,R.data=R.data||void 0,R})}function Pe(u,S){if(!u)return S();var E=u.req;E=E===void 0?{}:E;var R=E.options,Z=R===void 0?{}:R,re=Z.method,ce=re===void 0?"get":re;if(["post","put","patch","delete"].indexOf(ce.toLowerCase())===-1)return S();var ue=Z.requestType,he=ue===void 0?"json":ue,we=Z.data;if(we){var Oe=Object.prototype.toString.call(we);Oe==="[object Object]"||Oe==="[object Array]"?he==="json"?(Z.headers=N({Accept:"application/json","Content-Type":"application/json;charset=UTF-8"},Z.headers),Z.body=JSON.stringify(we)):he==="form"&&(Z.headers=N({Accept:"application/json","Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},Z.headers),Z.body=Ee(we)):(Z.headers=N({Accept:"application/json"},Z.headers),Z.body=we)}return u.req.options=Z,S()}function Ze(u,S){var E,R;if(u)if(S)E=S(u);else if(se(u))E=u.toString();else if(X(u))R=[],me(u,function(re){re===null||typeof re=="undefined"?R.push(re):R.push(pe(re)?JSON.stringify(re):re)}),E=Ee(R);else{R={},me(u,function(re,ce){var ue=re;re===null||typeof re=="undefined"?R[ce]=re:le(re)?ue=re.toISOString():X(re)?ue=re:pe(re)&&(ue=JSON.stringify(re)),R[ce]=ue});var Z=Ee(R);E=Z}return E}function Re(u,S){if(!u)return S();var E=u.req;E=E===void 0?{}:E;var R=E.options,Z=R===void 0?{}:R,re=Z.paramsSerializer,ce=Z.params,ue=u.req;ue=ue===void 0?{}:ue;var he=ue.url,we=he===void 0?"":he;Z.method=Z.method?Z.method.toUpperCase():"GET",Z.credentials=Z.credentials||"same-origin";var Oe=Ze(ce,re);if(u.req.originUrl=we,Oe){var Ue=we.indexOf("?")!==-1?"&":"?";u.req.url="".concat(we).concat(Ue).concat(Oe)}return u.req.options=Z,S()}var We=[Pe,Re,Me],$e=[Le];H.globalMiddlewares=We,H.defaultGlobalMiddlewaresLength=We.length,H.coreMiddlewares=$e,H.defaultCoreMiddlewaresLength=$e.length;var je=function(){function u(S){_(this,u),this.onion=new H([]),this.fetchIndex=0,this.mapCache=new ae(S),this.initOptions=S,this.instanceRequestInterceptors=[],this.instanceResponseInterceptors=[]}return g(u,[{key:"use",value:function(E){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!1,core:!1};return this.onion.use(E,R),this}},{key:"extendOptions",value:function(E){this.initOptions=_e(this.initOptions,E),this.mapCache.extendOptions(E)}},{key:"dealRequestInterceptors",value:function(E){var R=function(ce,ue){return ce.then(function(){var he=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return E.req.url=he.url||E.req.url,E.req.options=he.options||E.req.options,ue(E.req.url,E.req.options)})},Z=[].concat(f(u.requestInterceptors),f(this.instanceRequestInterceptors));return Z.reduce(R,Promise.resolve()).then(function(){var re=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return E.req.url=re.url||E.req.url,E.req.options=re.options||E.req.options,Promise.resolve()})}},{key:"request",value:function(E,R){var Z=this,re=this.onion,ce={req:{url:E,options:N(N({},R),{},{url:E})},res:null,cache:this.mapCache,responseInterceptors:[].concat(f(u.responseInterceptors),f(this.instanceResponseInterceptors))};if(typeof E!="string")throw new Error("url MUST be a string");return new Promise(function(ue,he){Z.dealRequestInterceptors(ce).then(function(){return re.execute(ce)}).then(function(){ue(ce.res)}).catch(function(we){var Oe=ce.req.options.errorHandler;if(Oe)try{var Ue=Oe(we);ue(Ue)}catch(l){he(l)}else he(we)})})}}],[{key:"requestUse",value:function(E){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!0};if(typeof E!="function")throw new TypeError("Interceptor must be function!");R.global?u.requestInterceptors.push(E):this.instanceRequestInterceptors.push(E)}},{key:"responseUse",value:function(E){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!0};if(typeof E!="function")throw new TypeError("Interceptor must be function!");R.global?u.responseInterceptors.push(E):this.instanceResponseInterceptors.push(E)}}]),u}();je.requestInterceptors=[Ce],je.responseInterceptors=[];function qe(u){this.message=u}qe.prototype.toString=function(){return this.message?"Cancel: ".concat(this.message):"Cancel"},qe.prototype.__CANCEL__=!0;function He(u){if(typeof u!="function")throw new TypeError("executor must be a function.");var S;this.promise=new Promise(function(Z){S=Z});var E=this;u(function(Z){E.reason||(E.reason=new qe(Z),S(E.reason))})}He.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},He.source=function(){var S,E=new He(function(Z){S=Z});return{token:E,cancel:S}};function Be(u){return!!(u&&u.__CANCEL__)}var Ne=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=new je(S),R=function(ce){var ue=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},he=_e(E.initOptions,ue);return E.request(ce,he)};R.use=E.use.bind(E),R.fetchIndex=E.fetchIndex,R.interceptors={request:{use:je.requestUse.bind(E)},response:{use:je.responseUse.bind(E)}};var Z=["get","post","delete","put","patch","head","options","rpc"];return Z.forEach(function(re){R[re]=function(ce,ue){return R(ce,N(N({},ue),{},{method:re}))}}),R.Cancel=qe,R.CancelToken=He,R.isCancel=Be,R.extendOptions=E.extendOptions.bind(E),R.middlewares={instance:E.onion.middlewares,defaultInstance:E.onion.defaultMiddlewares,global:H.globalMiddlewares,core:H.coreMiddlewares},R},De=function(S){return Ne(S)},ze=Ne({parseResponse:!1}),Ve=Ne({});Q.ZP=Ve},81308:function(be,Q,M){"use strict";M.d(Q,{A:function(){return o},Q:function(){return m}});var ee=M(73510),F=M(42549),K=M(24721),P=M(49338),j=M(57564),W=M(49556),N=M(86101),A=M(42208),_=M(18260);function O(p,b){if(!p)throw new Error(b)}function g(p){var b=p.fns,d=p.args;if(b.length===1)return b[0];var k=b.pop();return b.reduce(function(T,v){return function(){return v(T,d)}},k)}function w(p){return!!p&&(0,P.Z)(p)==="object"&&typeof p.then=="function"}var o=function(p){return p.compose="compose",p.modify="modify",p.event="event",p}({}),m=function(){function p(b){(0,N.Z)(this,p),(0,_.Z)(this,"opts",void 0),(0,_.Z)(this,"hooks",{}),this.opts=b}return(0,A.Z)(p,[{key:"register",value:function(d){var k=this;O(d.apply,"plugin register failed, apply must supplied"),Object.keys(d.apply).forEach(function(T){O(k.opts.validKeys.indexOf(T)>-1,"register failed, invalid key ".concat(T," ").concat(d.path?"from plugin ".concat(d.path):"",".")),k.hooks[T]=(k.hooks[T]||[]).concat(d.apply[T])})}},{key:"getHooks",value:function(d){var k=d.split("."),T=(0,W.Z)(k),v=T[0],c=T.slice(1),i=this.hooks[v]||[];return c.length&&(i=i.map(function(f){try{var C=f,I=(0,j.Z)(c),D;try{for(I.s();!(D=I.n()).done;){var G=D.value;C=C[G]}}catch(U){I.e(U)}finally{I.f()}return C}catch(U){return null}}).filter(Boolean)),i}},{key:"applyPlugins",value:function(d){var k=d.key,T=d.type,v=d.initialValue,c=d.args,i=d.async,f=this.getHooks(k)||[];switch(c&&O((0,P.Z)(c)==="object","applyPlugins failed, args must be plain object."),i&&O(T===o.modify||T===o.event,"async only works with modify and event type."),T){case o.modify:return i?f.reduce(function(){var C=(0,K.Z)((0,ee.Z)().mark(function I(D,G){var U;return(0,ee.Z)().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:if(O(typeof G=="function"||(0,P.Z)(G)==="object"||w(G),"applyPlugins failed, all hooks for key ".concat(k," must be function, plain object or Promise.")),!w(D)){H.next=5;break}return H.next=4,D;case 4:D=H.sent;case 5:if(typeof G!="function"){H.next=16;break}if(U=G(D,c),!w(U)){H.next=13;break}return H.next=10,U;case 10:return H.abrupt("return",H.sent);case 13:return H.abrupt("return",U);case 14:H.next=21;break;case 16:if(!w(G)){H.next=20;break}return H.next=19,G;case 19:G=H.sent;case 20:return H.abrupt("return",(0,F.Z)((0,F.Z)({},D),G));case 21:case"end":return H.stop()}},I)}));return function(I,D){return C.apply(this,arguments)}}(),w(v)?v:Promise.resolve(v)):f.reduce(function(C,I){return O(typeof I=="function"||(0,P.Z)(I)==="object","applyPlugins failed, all hooks for key ".concat(k," must be function or plain object.")),typeof I=="function"?I(C,c):(0,F.Z)((0,F.Z)({},C),I)},v);case o.event:return(0,K.Z)((0,ee.Z)().mark(function C(){var I,D,G,U;return(0,ee.Z)().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:I=(0,j.Z)(f),H.prev=1,I.s();case 3:if((D=I.n()).done){H.next=12;break}if(G=D.value,O(typeof G=="function","applyPlugins failed, all hooks for key ".concat(k," must be function.")),U=G(c),!(i&&w(U))){H.next=10;break}return H.next=10,U;case 10:H.next=3;break;case 12:H.next=17;break;case 14:H.prev=14,H.t0=H.catch(1),I.e(H.t0);case 17:return H.prev=17,I.f(),H.finish(17);case 20:case"end":return H.stop()}},C,null,[[1,14,17,20]])}))();case o.compose:return function(){return g({fns:f.concat(v),args:c})()}}}}],[{key:"create",value:function(d){var k=new p({validKeys:d.validKeys});return d.plugins.forEach(function(T){k.register(T)}),k}}]),p}()},11151:function(be,Q,M){"use strict";var ee=M(44194);function F(w,o){return w===o&&(w!==0||1/w===1/o)||w!==w&&o!==o}var K=typeof Object.is=="function"?Object.is:F,P=ee.useState,j=ee.useEffect,W=ee.useLayoutEffect,N=ee.useDebugValue;function A(w,o){var m=o(),p=P({inst:{value:m,getSnapshot:o}}),b=p[0].inst,d=p[1];return W(function(){b.value=m,b.getSnapshot=o,_(b)&&d({inst:b})},[w,m,o]),j(function(){return _(b)&&d({inst:b}),w(function(){_(b)&&d({inst:b})})},[w]),N(m),m}function _(w){var o=w.getSnapshot;w=w.value;try{var m=o();return!K(w,m)}catch(p){return!0}}function O(w,o){return o()}var g=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?O:A;Q.useSyncExternalStore=ee.useSyncExternalStore!==void 0?ee.useSyncExternalStore:g},94191:function(be,Q,M){"use strict";be.exports=M(11151)},82500:function(be,Q,M){"use strict";var ee;ee={value:!0},Q.AU=Q.Ts=void 0;const F=M(38069),K=M(42911),P=M(32650);var j;(function(_){_.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:P.Event.None}),_.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:P.Event.None});function O(g){const w=g;return w&&(w===_.None||w===_.Cancelled||K.boolean(w.isCancellationRequested)&&!!w.onCancellationRequested)}_.is=O})(j||(Q.Ts=j={}));const W=Object.freeze(function(_,O){const g=(0,F.default)().timer.setTimeout(_.bind(O),0);return{dispose(){g.dispose()}}});class N{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?W:(this._emitter||(this._emitter=new P.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class A{get token(){return this._token||(this._token=new N),this._token}cancel(){this._token?this._token.cancel():this._token=j.Cancelled}dispose(){this._token?this._token instanceof N&&this._token.dispose():this._token=j.None}}Q.AU=A},32650:function(be,Q,M){"use strict";Object.defineProperty(Q,"__esModule",{value:!0}),Q.Emitter=Q.Event=void 0;const ee=M(38069);var F;(function(j){const W={dispose(){}};j.None=function(){return W}})(F||(Q.Event=F={}));class K{add(W,N=null,A){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(W),this._contexts.push(N),Array.isArray(A)&&A.push({dispose:()=>this.remove(W,N)})}remove(W,N=null){if(!this._callbacks)return;let A=!1;for(let _=0,O=this._callbacks.length;_<O;_++)if(this._callbacks[_]===W)if(this._contexts[_]===N){this._callbacks.splice(_,1),this._contexts.splice(_,1);return}else A=!0;if(A)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...W){if(!this._callbacks)return[];const N=[],A=this._callbacks.slice(0),_=this._contexts.slice(0);for(let O=0,g=A.length;O<g;O++)try{N.push(A[O].apply(_[O],W))}catch(w){(0,ee.default)().console.error(w)}return N}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class P{constructor(W){this._options=W}get event(){return this._event||(this._event=(W,N,A)=>{this._callbacks||(this._callbacks=new K),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(W,N);const _={dispose:()=>{this._callbacks&&(this._callbacks.remove(W,N),_.dispose=P._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(A)&&A.push(_),_}),this._event}fire(W){this._callbacks&&this._callbacks.invoke.call(this._callbacks,W)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}Q.Emitter=P,P._noop=function(){}},42911:function(be,Q){"use strict";Object.defineProperty(Q,"__esModule",{value:!0}),Q.stringArray=Q.array=Q.func=Q.error=Q.number=Q.string=Q.boolean=void 0;function M(N){return N===!0||N===!1}Q.boolean=M;function ee(N){return typeof N=="string"||N instanceof String}Q.string=ee;function F(N){return typeof N=="number"||N instanceof Number}Q.number=F;function K(N){return N instanceof Error}Q.error=K;function P(N){return typeof N=="function"}Q.func=P;function j(N){return Array.isArray(N)}Q.array=j;function W(N){return j(N)&&N.every(A=>ee(A))}Q.stringArray=W},38069:function(be,Q){"use strict";Object.defineProperty(Q,"__esModule",{value:!0});let M;function ee(){if(M===void 0)throw new Error("No runtime abstraction layer installed");return M}(function(F){function K(P){if(P===void 0)throw new Error("No runtime abstraction layer provided");M=P}F.install=K})(ee||(ee={})),Q.default=ee},62118:function(be){"use strict";var Q=!1,M=function(){};if(Q){var ee=function(K,P){var j=arguments.length;P=new Array(j>1?j-1:0);for(var W=1;W<j;W++)P[W-1]=arguments[W];var N=0,A="Warning: "+K.replace(/%s/g,function(){return P[N++]});typeof console!="undefined"&&console.error(A);try{throw new Error(A)}catch(_){}};M=function(F,K,P){var j=arguments.length;P=new Array(j>2?j-2:0);for(var W=2;W<j;W++)P[W-2]=arguments[W];if(K===void 0)throw new Error("`warning(condition, format, ...args)` requires a warning message argument");F||ee.apply(null,[K].concat(P))}}be.exports=M},8004:function(be,Q,M){"use strict";M.d(Q,{ZP:function(){return c}});const ee=-1,F=0,K=1,P=2,j=3,W=4,N=5,A=6,_=7,O=8,g=typeof self=="object"?self:globalThis,w=(i,f)=>{const C=(D,G)=>(i.set(G,D),D),I=D=>{if(i.has(D))return i.get(D);const[G,U]=f[D];switch(G){case F:case ee:return C(U,D);case K:{const z=C([],D);for(const H of U)z.push(I(H));return z}case P:{const z=C({},D);for(const[H,ae]of U)z[I(H)]=I(ae);return z}case j:return C(new Date(U),D);case W:{const{source:z,flags:H}=U;return C(new RegExp(z,H),D)}case N:{const z=C(new Map,D);for(const[H,ae]of U)z.set(I(H),I(ae));return z}case A:{const z=C(new Set,D);for(const H of U)z.add(I(H));return z}case _:{const{name:z,message:H}=U;return C(new g[z](H),D)}case O:return C(BigInt(U),D);case"BigInt":return C(Object(BigInt(U)),D);case"ArrayBuffer":return C(new Uint8Array(U).buffer,U);case"DataView":{const{buffer:z}=new Uint8Array(U);return C(new DataView(z),U)}}return C(new g[G](U),D)};return I},o=i=>w(new Map,i)(0),m="",{toString:p}={},{keys:b}=Object,d=i=>{const f=typeof i;if(f!=="object"||!i)return[F,f];const C=p.call(i).slice(8,-1);switch(C){case"Array":return[K,m];case"Object":return[P,m];case"Date":return[j,m];case"RegExp":return[W,m];case"Map":return[N,m];case"Set":return[A,m];case"DataView":return[K,C]}return C.includes("Array")?[K,C]:C.includes("Error")?[_,C]:[P,C]},k=([i,f])=>i===F&&(f==="function"||f==="symbol"),T=(i,f,C,I)=>{const D=(U,z)=>{const H=I.push(U)-1;return C.set(z,H),H},G=U=>{if(C.has(U))return C.get(U);let[z,H]=d(U);switch(z){case F:{let q=U;switch(H){case"bigint":z=O,q=U.toString();break;case"function":case"symbol":if(i)throw new TypeError("unable to serialize "+H);q=null;break;case"undefined":return D([ee],U)}return D([z,q],U)}case K:{if(H){let J=U;return H==="DataView"?J=new Uint8Array(U.buffer):H==="ArrayBuffer"&&(J=new Uint8Array(U)),D([H,[...J]],U)}const q=[],L=D([z,q],U);for(const J of U)q.push(G(J));return L}case P:{if(H)switch(H){case"BigInt":return D([H,U.toString()],U);case"Boolean":case"Number":case"String":return D([H,U.valueOf()],U)}if(f&&"toJSON"in U)return G(U.toJSON());const q=[],L=D([z,q],U);for(const J of b(U))(i||!k(d(U[J])))&&q.push([G(J),G(U[J])]);return L}case j:return D([z,U.toISOString()],U);case W:{const{source:q,flags:L}=U;return D([z,{source:q,flags:L}],U)}case N:{const q=[],L=D([z,q],U);for(const[J,te]of U)(i||!(k(d(J))||k(d(te))))&&q.push([G(J),G(te)]);return L}case A:{const q=[],L=D([z,q],U);for(const J of U)(i||!k(d(J)))&&q.push(G(J));return L}}const{message:ae}=U;return D([z,{name:H,message:ae}],U)};return G},v=(i,{json:f,lossy:C}={})=>{const I=[];return T(!(f||C),!!f,new Map,I)(i),I};var c=typeof structuredClone=="function"?(i,f)=>f&&("json"in f||"lossy"in f)?o(v(i,f)):structuredClone(i):(i,f)=>o(v(i,f))},5715:function(be,Q,M){"use strict";M.d(Q,{l:function(){return O}});var ee=M(81050),F=M(93405),K=M(69245),P=M(89389),j=M(59521),W=M(55739);const N=function(v){const f=this.constructor.prototype,C=f[v],I=function(){return C.apply(I,arguments)};return Object.setPrototypeOf(I,f),I},A={}.hasOwnProperty;class _ extends N{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=(0,j.r)()}copy(){const c=new _;let i=-1;for(;++i<this.attachers.length;){const f=this.attachers[i];c.use(...f)}return c.data(F(!0,{},this.namespace)),c}data(c,i){return typeof c=="string"?arguments.length===2?(o("data",this.frozen),this.namespace[c]=i,this):A.call(this.namespace,c)&&this.namespace[c]||void 0:c?(o("data",this.frozen),this.namespace=c,this):this.namespace}freeze(){if(this.frozen)return this;const c=this;for(;++this.freezeIndex<this.attachers.length;){const[i,...f]=this.attachers[this.freezeIndex];if(f[0]===!1)continue;f[0]===!0&&(f[0]=void 0);const C=i.call(c,...f);typeof C=="function"&&this.transformers.use(C)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(c){this.freeze();const i=b(c),f=this.parser||this.Parser;return g("parse",f),f(String(i),i)}process(c,i){const f=this;return this.freeze(),g("process",this.parser||this.Parser),w("process",this.compiler||this.Compiler),i?C(void 0,i):new Promise(C);function C(I,D){const G=b(c),U=f.parse(G);f.run(U,G,function(H,ae,q){if(H||!ae||!q)return z(H);const L=ae,J=f.stringify(L,q);k(J)?q.value=J:q.result=J,z(H,q)});function z(H,ae){H||!ae?D(H):I?I(ae):((0,K.ok)(i,"`done` is defined if `resolve` is not"),i(void 0,ae))}}}processSync(c){let i=!1,f;return this.freeze(),g("processSync",this.parser||this.Parser),w("processSync",this.compiler||this.Compiler),this.process(c,C),p("processSync","process",i),(0,K.ok)(f,"we either bailed on an error or have a tree"),f;function C(I,D){i=!0,(0,ee.N)(I),f=D}}run(c,i,f){m(c),this.freeze();const C=this.transformers;return!f&&typeof i=="function"&&(f=i,i=void 0),f?I(void 0,f):new Promise(I);function I(D,G){(0,K.ok)(typeof i!="function","`file` can\u2019t be a `done` anymore, we checked");const U=b(i);C.run(c,U,z);function z(H,ae,q){const L=ae||c;H?G(H):D?D(L):((0,K.ok)(f,"`done` is defined if `resolve` is not"),f(void 0,L,q))}}}runSync(c,i){let f=!1,C;return this.run(c,i,I),p("runSync","run",f),(0,K.ok)(C,"we either bailed on an error or have a tree"),C;function I(D,G){(0,ee.N)(D),C=G,f=!0}}stringify(c,i){this.freeze();const f=b(i),C=this.compiler||this.Compiler;return w("stringify",C),m(c),C(c,f)}use(c,...i){const f=this.attachers,C=this.namespace;if(o("use",this.frozen),c!=null)if(typeof c=="function")U(c,i);else if(typeof c=="object")Array.isArray(c)?G(c):D(c);else throw new TypeError("Expected usable value, not `"+c+"`");return this;function I(z){if(typeof z=="function")U(z,[]);else if(typeof z=="object")if(Array.isArray(z)){const[H,...ae]=z;U(H,ae)}else D(z);else throw new TypeError("Expected usable value, not `"+z+"`")}function D(z){if(!("plugins"in z)&&!("settings"in z))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");G(z.plugins),z.settings&&(C.settings=F(!0,C.settings,z.settings))}function G(z){let H=-1;if(z!=null)if(Array.isArray(z))for(;++H<z.length;){const ae=z[H];I(ae)}else throw new TypeError("Expected a list of plugins, not `"+z+"`")}function U(z,H){let ae=-1,q=-1;for(;++ae<f.length;)if(f[ae][0]===z){q=ae;break}if(q===-1)f.push([z,...H]);else if(H.length>0){let[L,...J]=H;const te=f[q][1];(0,P.Z)(te)&&(0,P.Z)(L)&&(L=F(!0,te,L)),f[q]=[z,L,...J]}}}}const O=new _().freeze();function g(v,c){if(typeof c!="function")throw new TypeError("Cannot `"+v+"` without `parser`")}function w(v,c){if(typeof c!="function")throw new TypeError("Cannot `"+v+"` without `compiler`")}function o(v,c){if(c)throw new Error("Cannot call `"+v+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function m(v){if(!(0,P.Z)(v)||typeof v.type!="string")throw new TypeError("Expected node, got `"+v+"`")}function p(v,c,i){if(!i)throw new Error("`"+v+"` finished async. Use `"+c+"` instead")}function b(v){return d(v)?v:new W.k(v)}function d(v){return!!(v&&typeof v=="object"&&"message"in v&&"messages"in v)}function k(v){return typeof v=="string"||T(v)}function T(v){return!!(v&&typeof v=="object"&&"byteLength"in v&&"byteOffset"in v)}},83368:function(be,Q,M){"use strict";M.d(Q,{U:function(){return F}});var ee=M(6606);const F=function(K,P,j){const W=(0,ee.O)(j);if(!K||!K.type||!K.children)throw new Error("Expected parent node");if(typeof P=="number"){if(P<0||P===Number.POSITIVE_INFINITY)throw new Error("Expected positive finite number as index")}else if(P=K.children.indexOf(P),P<0)throw new Error("Expected child node or index");for(;++P<K.children.length;)if(W(K.children[P],P,K))return K.children[P]}},6606:function(be,Q,M){"use strict";M.d(Q,{O:function(){return F}});const ee=function(_,O,g,w,o){const m=F(O);if(g!=null&&(typeof g!="number"||g<0||g===Number.POSITIVE_INFINITY))throw new Error("Expected positive finite index");if(w!=null&&(!ee(w)||!w.children))throw new Error("Expected parent node");if(w==null!=(g==null))throw new Error("Expected both parent and index");return A(_)?m.call(o,_,g,w):!1},F=function(_){if(_==null)return N;if(typeof _=="function")return W(_);if(typeof _=="object")return Array.isArray(_)?K(_):P(_);if(typeof _=="string")return j(_);throw new Error("Expected function, string, or object as test")};function K(_){const O=[];let g=-1;for(;++g<_.length;)O[g]=F(_[g]);return W(w);function w(...o){let m=-1;for(;++m<O.length;)if(O[m].apply(this,o))return!0;return!1}}function P(_){const O=_;return W(g);function g(w){const o=w;let m;for(m in _)if(o[m]!==O[m])return!1;return!0}}function j(_){return W(O);function O(g){return g&&g.type===_}}function W(_){return O;function O(g,w,o){return!!(A(g)&&_.call(this,g,typeof w=="number"?w:void 0,o||void 0))}}function N(){return!0}function A(_){return _!==null&&typeof _=="object"&&"type"in _}},34183:function(be,Q,M){"use strict";M.d(Q,{FK:function(){return P},Pk:function(){return F},rb:function(){return ee}});const ee=K("end"),F=K("start");function K(j){return W;function W(N){const A=N&&N.position&&N.position[j]||{};if(typeof A.line=="number"&&A.line>0&&typeof A.column=="number"&&A.column>0)return{line:A.line,column:A.column,offset:typeof A.offset=="number"&&A.offset>-1?A.offset:void 0}}}function P(j){const W=F(j),N=ee(j);if(W&&N)return{start:W,end:N}}},40014:function(be,Q,M){"use strict";M.d(Q,{y:function(){return ee}});function ee(j){return!j||typeof j!="object"?"":"position"in j||"type"in j?K(j.position):"start"in j||"end"in j?K(j):"line"in j||"column"in j?F(j):""}function F(j){return P(j&&j.line)+":"+P(j&&j.column)}function K(j){return F(j&&j.start)+"-"+F(j&&j.end)}function P(j){return j&&typeof j=="number"?j:1}},19517:function(be,Q,M){"use strict";M.d(Q,{BK:function(){return j},S4:function(){return N}});var ee=M(6606);function F(_){return _}const K=[],P=!0,j=!1,W="skip";function N(_,O,g,w){let o;typeof O=="function"&&typeof g!="function"?(w=g,g=O):o=O;const m=(0,ee.O)(o),p=w?-1:1;b(_,void 0,[])();function b(d,k,T){const v=d&&typeof d=="object"?d:{};if(typeof v.type=="string"){const i=typeof v.tagName=="string"?v.tagName:typeof v.name=="string"?v.name:void 0;Object.defineProperty(c,"name",{value:"node ("+(d.type+(i?"<"+i+">":""))+")"})}return c;function c(){let i=K,f,C,I;if((!O||m(d,k,T[T.length-1]||void 0))&&(i=A(g(d,T)),i[0]===j))return i;if("children"in d&&d.children){const D=d;if(D.children&&i[0]!==W)for(C=(w?D.children.length:-1)+p,I=T.concat(D);C>-1&&C<D.children.length;){const G=D.children[C];if(f=b(G,C,I)(),f[0]===j)return f;C=typeof f[1]=="number"?f[1]:C+p}}return i}}}function A(_){return Array.isArray(_)?_:typeof _=="number"?[P,_]:_==null?K:[_]}},92662:function(be,Q,M){"use strict";M.d(Q,{Vn:function(){return F}});var ee=M(19517);function F(K,P,j,W){let N,A,_;typeof P=="function"&&typeof j!="function"?(A=void 0,_=P,N=j):(A=P,_=j,N=W),(0,ee.S4)(K,A,O,N);function O(g,w){const o=w[w.length-1],m=o?o.children.indexOf(g):void 0;return _(g,m,o)}}},38029:function(be,Q,M){"use strict";M.d(Q,{$:function(){return F}});var ee=M(40014);class F extends Error{constructor(P,j,W){super(),typeof j=="string"&&(W=j,j=void 0);let N="",A={},_=!1;if(j&&("line"in j&&"column"in j?A={place:j}:"start"in j&&"end"in j?A={place:j}:"type"in j?A={ancestors:[j],place:j.position}:A=Ke({},j)),typeof P=="string"?N=P:!A.cause&&P&&(_=!0,N=P.message,A.cause=P),!A.ruleId&&!A.source&&typeof W=="string"){const g=W.indexOf(":");g===-1?A.ruleId=W:(A.source=W.slice(0,g),A.ruleId=W.slice(g+1))}if(!A.place&&A.ancestors&&A.ancestors){const g=A.ancestors[A.ancestors.length-1];g&&(A.place=g.position)}const O=A.place&&"start"in A.place?A.place.start:A.place;this.ancestors=A.ancestors||void 0,this.cause=A.cause||void 0,this.column=O?O.column:void 0,this.fatal=void 0,this.file,this.message=N,this.line=O?O.line:void 0,this.name=(0,ee.y)(A.place)||"1:1",this.place=A.place||void 0,this.reason=this.message,this.ruleId=A.ruleId||void 0,this.source=A.source||void 0,this.stack=_&&A.cause&&typeof A.cause.stack=="string"?A.cause.stack:"",this.actual,this.expected,this.note,this.url}}F.prototype.file="",F.prototype.name="",F.prototype.reason="",F.prototype.message="",F.prototype.stack="",F.prototype.column=void 0,F.prototype.line=void 0,F.prototype.ancestors=void 0,F.prototype.cause=void 0,F.prototype.fatal=void 0,F.prototype.place=void 0,F.prototype.ruleId=void 0,F.prototype.source=void 0},55739:function(be,Q,M){"use strict";M.d(Q,{k:function(){return b}});var ee=M(38029);const F={basename:K,dirname:P,extname:j,join:W,sep:"/"};function K(c,i){if(i!==void 0&&typeof i!="string")throw new TypeError('"ext" argument must be a string');_(c);let f=0,C=-1,I=c.length,D;if(i===void 0||i.length===0||i.length>c.length){for(;I--;)if(c.codePointAt(I)===47){if(D){f=I+1;break}}else C<0&&(D=!0,C=I+1);return C<0?"":c.slice(f,C)}if(i===c)return"";let G=-1,U=i.length-1;for(;I--;)if(c.codePointAt(I)===47){if(D){f=I+1;break}}else G<0&&(D=!0,G=I+1),U>-1&&(c.codePointAt(I)===i.codePointAt(U--)?U<0&&(C=I):(U=-1,C=G));return f===C?C=G:C<0&&(C=c.length),c.slice(f,C)}function P(c){if(_(c),c.length===0)return".";let i=-1,f=c.length,C;for(;--f;)if(c.codePointAt(f)===47){if(C){i=f;break}}else C||(C=!0);return i<0?c.codePointAt(0)===47?"/":".":i===1&&c.codePointAt(0)===47?"//":c.slice(0,i)}function j(c){_(c);let i=c.length,f=-1,C=0,I=-1,D=0,G;for(;i--;){const U=c.codePointAt(i);if(U===47){if(G){C=i+1;break}continue}f<0&&(G=!0,f=i+1),U===46?I<0?I=i:D!==1&&(D=1):I>-1&&(D=-1)}return I<0||f<0||D===0||D===1&&I===f-1&&I===C+1?"":c.slice(I,f)}function W(...c){let i=-1,f;for(;++i<c.length;)_(c[i]),c[i]&&(f=f===void 0?c[i]:f+"/"+c[i]);return f===void 0?".":N(f)}function N(c){_(c);const i=c.codePointAt(0)===47;let f=A(c,!i);return f.length===0&&!i&&(f="."),f.length>0&&c.codePointAt(c.length-1)===47&&(f+="/"),i?"/"+f:f}function A(c,i){let f="",C=0,I=-1,D=0,G=-1,U,z;for(;++G<=c.length;){if(G<c.length)U=c.codePointAt(G);else{if(U===47)break;U=47}if(U===47){if(!(I===G-1||D===1))if(I!==G-1&&D===2){if(f.length<2||C!==2||f.codePointAt(f.length-1)!==46||f.codePointAt(f.length-2)!==46){if(f.length>2){if(z=f.lastIndexOf("/"),z!==f.length-1){z<0?(f="",C=0):(f=f.slice(0,z),C=f.length-1-f.lastIndexOf("/")),I=G,D=0;continue}}else if(f.length>0){f="",C=0,I=G,D=0;continue}}i&&(f=f.length>0?f+"/..":"..",C=2)}else f.length>0?f+="/"+c.slice(I+1,G):f=c.slice(I+1,G),C=G-I-1;I=G,D=0}else U===46&&D>-1?D++:D=-1}return f}function _(c){if(typeof c!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(c))}const O={cwd:g};function g(){return"/"}function w(c){return!!(c!==null&&typeof c=="object"&&"href"in c&&c.href&&"protocol"in c&&c.protocol&&c.auth===void 0)}function o(c){if(typeof c=="string")c=new URL(c);else if(!w(c)){const i=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+c+"`");throw i.code="ERR_INVALID_ARG_TYPE",i}if(c.protocol!=="file:"){const i=new TypeError("The URL must be of scheme file");throw i.code="ERR_INVALID_URL_SCHEME",i}return m(c)}function m(c){if(c.hostname!==""){const C=new TypeError('File URL host must be "localhost" or empty on darwin');throw C.code="ERR_INVALID_FILE_URL_HOST",C}const i=c.pathname;let f=-1;for(;++f<i.length;)if(i.codePointAt(f)===37&&i.codePointAt(f+1)===50){const C=i.codePointAt(f+2);if(C===70||C===102){const I=new TypeError("File URL path must not include encoded / characters");throw I.code="ERR_INVALID_FILE_URL_PATH",I}}return decodeURIComponent(i)}const p=["history","path","basename","stem","extname","dirname"];class b{constructor(i){let f;i?w(i)?f={path:i}:typeof i=="string"||v(i)?f={value:i}:f=i:f={},this.cwd="cwd"in f?"":O.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let C=-1;for(;++C<p.length;){const D=p[C];D in f&&f[D]!==void 0&&f[D]!==null&&(this[D]=D==="history"?[...f[D]]:f[D])}let I;for(I in f)p.includes(I)||(this[I]=f[I])}get basename(){return typeof this.path=="string"?F.basename(this.path):void 0}set basename(i){k(i,"basename"),d(i,"basename"),this.path=F.join(this.dirname||"",i)}get dirname(){return typeof this.path=="string"?F.dirname(this.path):void 0}set dirname(i){T(this.basename,"dirname"),this.path=F.join(i||"",this.basename)}get extname(){return typeof this.path=="string"?F.extname(this.path):void 0}set extname(i){if(d(i,"extname"),T(this.dirname,"extname"),i){if(i.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(i.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=F.join(this.dirname,this.stem+(i||""))}get path(){return this.history[this.history.length-1]}set path(i){w(i)&&(i=o(i)),k(i,"path"),this.path!==i&&this.history.push(i)}get stem(){return typeof this.path=="string"?F.basename(this.path,this.extname):void 0}set stem(i){k(i,"stem"),d(i,"stem"),this.path=F.join(this.dirname||"",i+(this.extname||""))}fail(i,f,C){const I=this.message(i,f,C);throw I.fatal=!0,I}info(i,f,C){const I=this.message(i,f,C);return I.fatal=void 0,I}message(i,f,C){const I=new ee.$(i,f,C);return this.path&&(I.name=this.path+":"+I.name,I.file=this.path),I.fatal=!1,this.messages.push(I),I}toString(i){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(i||void 0).decode(this.value)}}function d(c,i){if(c&&c.includes(F.sep))throw new Error("`"+i+"` cannot be a path: did not expect `"+F.sep+"`")}function k(c,i){if(!c)throw new Error("`"+i+"` cannot be empty")}function T(c,i){if(!c)throw new Error("Setting `"+i+"` requires `path` to be set too")}function v(c){return!!(c&&typeof c=="object"&&"byteLength"in c&&"byteOffset"in c)}},2792:function(be,Q,M){"use strict";M.d(Q,{n:function(){return F}});class ee{constructor(_,O,g,w){this._uri=_,this._languageId=O,this._version=g,this._content=w,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(_){if(_){const O=this.offsetAt(_.start),g=this.offsetAt(_.end);return this._content.substring(O,g)}return this._content}update(_,O){for(const g of _)if(ee.isIncremental(g)){const w=W(g.range),o=this.offsetAt(w.start),m=this.offsetAt(w.end);this._content=this._content.substring(0,o)+g.text+this._content.substring(m,this._content.length);const p=Math.max(w.start.line,0),b=Math.max(w.end.line,0);let d=this._lineOffsets;const k=P(g.text,!1,o);if(b-p===k.length)for(let v=0,c=k.length;v<c;v++)d[v+p+1]=k[v];else k.length<1e4?d.splice(p+1,b-p,...k):this._lineOffsets=d=d.slice(0,p+1).concat(k,d.slice(b+1));const T=g.text.length-(m-o);if(T!==0)for(let v=p+1+k.length,c=d.length;v<c;v++)d[v]=d[v]+T}else if(ee.isFull(g))this._content=g.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=O}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=P(this._content,!0)),this._lineOffsets}positionAt(_){_=Math.max(Math.min(_,this._content.length),0);const O=this.getLineOffsets();let g=0,w=O.length;if(w===0)return{line:0,character:_};for(;g<w;){const m=Math.floor((g+w)/2);O[m]>_?w=m:g=m+1}const o=g-1;return _=this.ensureBeforeEOL(_,O[o]),{line:o,character:_-O[o]}}offsetAt(_){const O=this.getLineOffsets();if(_.line>=O.length)return this._content.length;if(_.line<0)return 0;const g=O[_.line];if(_.character<=0)return g;const w=_.line+1<O.length?O[_.line+1]:this._content.length,o=Math.min(g+_.character,w);return this.ensureBeforeEOL(o,g)}ensureBeforeEOL(_,O){for(;_>O&&j(this._content.charCodeAt(_-1));)_--;return _}get lineCount(){return this.getLineOffsets().length}static isIncremental(_){const O=_;return O!=null&&typeof O.text=="string"&&O.range!==void 0&&(O.rangeLength===void 0||typeof O.rangeLength=="number")}static isFull(_){const O=_;return O!=null&&typeof O.text=="string"&&O.range===void 0&&O.rangeLength===void 0}}var F;(function(A){function _(w,o,m,p){return new ee(w,o,m,p)}A.create=_;function O(w,o,m){if(w instanceof ee)return w.update(o,m),w;throw new Error("TextDocument.update: document must be created by TextDocument.create")}A.update=O;function g(w,o){const m=w.getText(),p=K(o.map(N),(k,T)=>{const v=k.range.start.line-T.range.start.line;return v===0?k.range.start.character-T.range.start.character:v});let b=0;const d=[];for(const k of p){const T=w.offsetAt(k.range.start);if(T<b)throw new Error("Overlapping edit");T>b&&d.push(m.substring(b,T)),k.newText.length&&d.push(k.newText),b=w.offsetAt(k.range.end)}return d.push(m.substr(b)),d.join("")}A.applyEdits=g})(F||(F={}));function K(A,_){if(A.length<=1)return A;const O=A.length/2|0,g=A.slice(0,O),w=A.slice(O);K(g,_),K(w,_);let o=0,m=0,p=0;for(;o<g.length&&m<w.length;)_(g[o],w[m])<=0?A[p++]=g[o++]:A[p++]=w[m++];for(;o<g.length;)A[p++]=g[o++];for(;m<w.length;)A[p++]=w[m++];return A}function P(A,_,O=0){const g=_?[O]:[];for(let w=0;w<A.length;w++){const o=A.charCodeAt(w);j(o)&&(o===13&&w+1<A.length&&A.charCodeAt(w+1)===10&&w++,g.push(O+w+1))}return g}function j(A){return A===13||A===10}function W(A){const _=A.start,O=A.end;return _.line>O.line||_.line===O.line&&_.character>O.character?{start:O,end:_}:A}function N(A){const _=W(A.range);return _!==A.range?{newText:A.newText,range:_}:A}},87541:function(be,Q,M){"use strict";M.d(Q,{Ly:function(){return j},e6:function(){return W}});var ee;(function(t){function r(a){return typeof a=="string"}t.is=r})(ee||(ee={}));var F;(function(t){function r(a){return typeof a=="string"}t.is=r})(F||(F={}));var K;(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function r(a){return typeof a=="number"&&t.MIN_VALUE<=a&&a<=t.MAX_VALUE}t.is=r})(K||(K={}));var P;(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function r(a){return typeof a=="number"&&t.MIN_VALUE<=a&&a<=t.MAX_VALUE}t.is=r})(P||(P={}));var j;(function(t){function r(n,e){return n===Number.MAX_VALUE&&(n=P.MAX_VALUE),e===Number.MAX_VALUE&&(e=P.MAX_VALUE),{line:n,character:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&s.uinteger(e.line)&&s.uinteger(e.character)}t.is=a})(j||(j={}));var W;(function(t){function r(n,e,x,V){if(s.uinteger(n)&&s.uinteger(e)&&s.uinteger(x)&&s.uinteger(V))return{start:j.create(n,e),end:j.create(x,V)};if(j.is(n)&&j.is(e))return{start:n,end:e};throw new Error(`Range#create called with invalid arguments[${n}, ${e}, ${x}, ${V}]`)}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&j.is(e.start)&&j.is(e.end)}t.is=a})(W||(W={}));var N;(function(t){function r(n,e){return{uri:n,range:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.range)&&(s.string(e.uri)||s.undefined(e.uri))}t.is=a})(N||(N={}));var A;(function(t){function r(n,e,x,V){return{targetUri:n,targetRange:e,targetSelectionRange:x,originSelectionRange:V}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.targetRange)&&s.string(e.targetUri)&&W.is(e.targetSelectionRange)&&(W.is(e.originSelectionRange)||s.undefined(e.originSelectionRange))}t.is=a})(A||(A={}));var _;(function(t){function r(n,e,x,V){return{red:n,green:e,blue:x,alpha:V}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.numberRange(e.red,0,1)&&s.numberRange(e.green,0,1)&&s.numberRange(e.blue,0,1)&&s.numberRange(e.alpha,0,1)}t.is=a})(_||(_={}));var O;(function(t){function r(n,e){return{range:n,color:e}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&W.is(e.range)&&_.is(e.color)}t.is=a})(O||(O={}));var g;(function(t){function r(n,e,x){return{label:n,textEdit:e,additionalTextEdits:x}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.string(e.label)&&(s.undefined(e.textEdit)||v.is(e))&&(s.undefined(e.additionalTextEdits)||s.typedArray(e.additionalTextEdits,v.is))}t.is=a})(g||(g={}));var w;(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(w||(w={}));var o;(function(t){function r(n,e,x,V,de,xe){const Ae={startLine:n,endLine:e};return s.defined(x)&&(Ae.startCharacter=x),s.defined(V)&&(Ae.endCharacter=V),s.defined(de)&&(Ae.kind=de),s.defined(xe)&&(Ae.collapsedText=xe),Ae}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.uinteger(e.startLine)&&s.uinteger(e.startLine)&&(s.undefined(e.startCharacter)||s.uinteger(e.startCharacter))&&(s.undefined(e.endCharacter)||s.uinteger(e.endCharacter))&&(s.undefined(e.kind)||s.string(e.kind))}t.is=a})(o||(o={}));var m;(function(t){function r(n,e){return{location:n,message:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&N.is(e.location)&&s.string(e.message)}t.is=a})(m||(m={}));var p;(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(p||(p={}));var b;(function(t){t.Unnecessary=1,t.Deprecated=2})(b||(b={}));var d;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&s.string(n.href)}t.is=r})(d||(d={}));var k;(function(t){function r(n,e,x,V,de,xe){let Ae={range:n,message:e};return s.defined(x)&&(Ae.severity=x),s.defined(V)&&(Ae.code=V),s.defined(de)&&(Ae.source=de),s.defined(xe)&&(Ae.relatedInformation=xe),Ae}t.create=r;function a(n){var e;let x=n;return s.defined(x)&&W.is(x.range)&&s.string(x.message)&&(s.number(x.severity)||s.undefined(x.severity))&&(s.integer(x.code)||s.string(x.code)||s.undefined(x.code))&&(s.undefined(x.codeDescription)||s.string((e=x.codeDescription)===null||e===void 0?void 0:e.href))&&(s.string(x.source)||s.undefined(x.source))&&(s.undefined(x.relatedInformation)||s.typedArray(x.relatedInformation,m.is))}t.is=a})(k||(k={}));var T;(function(t){function r(n,e,...x){let V={title:n,command:e};return s.defined(x)&&x.length>0&&(V.arguments=x),V}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.title)&&s.string(e.command)}t.is=a})(T||(T={}));var v;(function(t){function r(x,V){return{range:x,newText:V}}t.replace=r;function a(x,V){return{range:{start:x,end:x},newText:V}}t.insert=a;function n(x){return{range:x,newText:""}}t.del=n;function e(x){const V=x;return s.objectLiteral(V)&&s.string(V.newText)&&W.is(V.range)}t.is=e})(v||(v={}));var c;(function(t){function r(n,e,x){const V={label:n};return e!==void 0&&(V.needsConfirmation=e),x!==void 0&&(V.description=x),V}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.string(e.label)&&(s.boolean(e.needsConfirmation)||e.needsConfirmation===void 0)&&(s.string(e.description)||e.description===void 0)}t.is=a})(c||(c={}));var i;(function(t){function r(a){const n=a;return s.string(n)}t.is=r})(i||(i={}));var f;(function(t){function r(x,V,de){return{range:x,newText:V,annotationId:de}}t.replace=r;function a(x,V,de){return{range:{start:x,end:x},newText:V,annotationId:de}}t.insert=a;function n(x,V){return{range:x,newText:"",annotationId:V}}t.del=n;function e(x){const V=x;return v.is(V)&&(c.is(V.annotationId)||i.is(V.annotationId))}t.is=e})(f||(f={}));var C;(function(t){function r(n,e){return{textDocument:n,edits:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&J.is(e.textDocument)&&Array.isArray(e.edits)}t.is=a})(C||(C={}));var I;(function(t){function r(n,e,x){let V={kind:"create",uri:n};return e!==void 0&&(e.overwrite!==void 0||e.ignoreIfExists!==void 0)&&(V.options=e),x!==void 0&&(V.annotationId=x),V}t.create=r;function a(n){let e=n;return e&&e.kind==="create"&&s.string(e.uri)&&(e.options===void 0||(e.options.overwrite===void 0||s.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||s.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(I||(I={}));var D;(function(t){function r(n,e,x,V){let de={kind:"rename",oldUri:n,newUri:e};return x!==void 0&&(x.overwrite!==void 0||x.ignoreIfExists!==void 0)&&(de.options=x),V!==void 0&&(de.annotationId=V),de}t.create=r;function a(n){let e=n;return e&&e.kind==="rename"&&s.string(e.oldUri)&&s.string(e.newUri)&&(e.options===void 0||(e.options.overwrite===void 0||s.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||s.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(D||(D={}));var G;(function(t){function r(n,e,x){let V={kind:"delete",uri:n};return e!==void 0&&(e.recursive!==void 0||e.ignoreIfNotExists!==void 0)&&(V.options=e),x!==void 0&&(V.annotationId=x),V}t.create=r;function a(n){let e=n;return e&&e.kind==="delete"&&s.string(e.uri)&&(e.options===void 0||(e.options.recursive===void 0||s.boolean(e.options.recursive))&&(e.options.ignoreIfNotExists===void 0||s.boolean(e.options.ignoreIfNotExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(G||(G={}));var U;(function(t){function r(a){let n=a;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(e=>s.string(e.kind)?I.is(e)||D.is(e)||G.is(e):C.is(e)))}t.is=r})(U||(U={}));class z{constructor(r,a){this.edits=r,this.changeAnnotations=a}insert(r,a,n){let e,x;if(n===void 0?e=v.insert(r,a):i.is(n)?(x=n,e=f.insert(r,a,n)):(this.assertChangeAnnotations(this.changeAnnotations),x=this.changeAnnotations.manage(n),e=f.insert(r,a,x)),this.edits.push(e),x!==void 0)return x}replace(r,a,n){let e,x;if(n===void 0?e=v.replace(r,a):i.is(n)?(x=n,e=f.replace(r,a,n)):(this.assertChangeAnnotations(this.changeAnnotations),x=this.changeAnnotations.manage(n),e=f.replace(r,a,x)),this.edits.push(e),x!==void 0)return x}delete(r,a){let n,e;if(a===void 0?n=v.del(r):i.is(a)?(e=a,n=f.del(r,a)):(this.assertChangeAnnotations(this.changeAnnotations),e=this.changeAnnotations.manage(a),n=f.del(r,e)),this.edits.push(n),e!==void 0)return e}add(r){this.edits.push(r)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(r){if(r===void 0)throw new Error("Text edit change is not configured to manage change annotations.")}}class H{constructor(r){this._annotations=r===void 0?Object.create(null):r,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(r,a){let n;if(i.is(r)?n=r:(n=this.nextId(),a=r),this._annotations[n]!==void 0)throw new Error(`Id ${n} is already in use.`);if(a===void 0)throw new Error(`No annotation provided for id ${n}`);return this._annotations[n]=a,this._size++,n}nextId(){return this._counter++,this._counter.toString()}}class ae{constructor(r){this._textEditChanges=Object.create(null),r!==void 0?(this._workspaceEdit=r,r.documentChanges?(this._changeAnnotations=new H(r.changeAnnotations),r.changeAnnotations=this._changeAnnotations.all(),r.documentChanges.forEach(a=>{if(C.is(a)){const n=new z(a.edits,this._changeAnnotations);this._textEditChanges[a.textDocument.uri]=n}})):r.changes&&Object.keys(r.changes).forEach(a=>{const n=new z(r.changes[a]);this._textEditChanges[a]=n})):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(r){if(J.is(r)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");const a={uri:r.uri,version:r.version};let n=this._textEditChanges[a.uri];if(!n){const e=[],x={textDocument:a,edits:e};this._workspaceEdit.documentChanges.push(x),n=new z(e,this._changeAnnotations),this._textEditChanges[a.uri]=n}return n}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");let a=this._textEditChanges[r];if(!a){let n=[];this._workspaceEdit.changes[r]=n,a=new z(n),this._textEditChanges[r]=a}return a}}initDocumentChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new H,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))}createFile(r,a,n){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let e;c.is(a)||i.is(a)?e=a:n=a;let x,V;if(e===void 0?x=I.create(r,n):(V=i.is(e)?e:this._changeAnnotations.manage(e),x=I.create(r,n,V)),this._workspaceEdit.documentChanges.push(x),V!==void 0)return V}renameFile(r,a,n,e){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let x;c.is(n)||i.is(n)?x=n:e=n;let V,de;if(x===void 0?V=D.create(r,a,e):(de=i.is(x)?x:this._changeAnnotations.manage(x),V=D.create(r,a,e,de)),this._workspaceEdit.documentChanges.push(V),de!==void 0)return de}deleteFile(r,a,n){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let e;c.is(a)||i.is(a)?e=a:n=a;let x,V;if(e===void 0?x=G.create(r,n):(V=i.is(e)?e:this._changeAnnotations.manage(e),x=G.create(r,n,V)),this._workspaceEdit.documentChanges.push(x),V!==void 0)return V}}var q;(function(t){function r(n){return{uri:n}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)}t.is=a})(q||(q={}));var L;(function(t){function r(n,e){return{uri:n,version:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&s.integer(e.version)}t.is=a})(L||(L={}));var J;(function(t){function r(n,e){return{uri:n,version:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&(e.version===null||s.integer(e.version))}t.is=a})(J||(J={}));var te;(function(t){function r(n,e,x,V){return{uri:n,languageId:e,version:x,text:V}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&s.string(e.languageId)&&s.integer(e.version)&&s.string(e.text)}t.is=a})(te||(te={}));var ye;(function(t){t.PlainText="plaintext",t.Markdown="markdown";function r(a){const n=a;return n===t.PlainText||n===t.Markdown}t.is=r})(ye||(ye={}));var fe;(function(t){function r(a){const n=a;return s.objectLiteral(a)&&ye.is(n.kind)&&s.string(n.value)}t.is=r})(fe||(fe={}));var oe;(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(oe||(oe={}));var B;(function(t){t.PlainText=1,t.Snippet=2})(B||(B={}));var X;(function(t){t.Deprecated=1})(X||(X={}));var se;(function(t){function r(n,e,x){return{newText:n,insert:e,replace:x}}t.create=r;function a(n){const e=n;return e&&s.string(e.newText)&&W.is(e.insert)&&W.is(e.replace)}t.is=a})(se||(se={}));var le;(function(t){t.asIs=1,t.adjustIndentation=2})(le||(le={}));var pe;(function(t){function r(a){const n=a;return n&&(s.string(n.detail)||n.detail===void 0)&&(s.string(n.description)||n.description===void 0)}t.is=r})(pe||(pe={}));var me;(function(t){function r(a){return{label:a}}t.create=r})(me||(me={}));var ge;(function(t){function r(a,n){return{items:a||[],isIncomplete:!!n}}t.create=r})(ge||(ge={}));var Ee;(function(t){function r(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}t.fromPlainText=r;function a(n){const e=n;return s.string(e)||s.objectLiteral(e)&&s.string(e.language)&&s.string(e.value)}t.is=a})(Ee||(Ee={}));var _e;(function(t){function r(a){let n=a;return!!n&&s.objectLiteral(n)&&(fe.is(n.contents)||Ee.is(n.contents)||s.typedArray(n.contents,Ee.is))&&(a.range===void 0||W.is(a.range))}t.is=r})(_e||(_e={}));var Ce;(function(t){function r(a,n){return n?{label:a,documentation:n}:{label:a}}t.create=r})(Ce||(Ce={}));var Se;(function(t){function r(a,n,...e){let x={label:a};return s.defined(n)&&(x.documentation=n),s.defined(e)?x.parameters=e:x.parameters=[],x}t.create=r})(Se||(Se={}));var Ie;(function(t){t.Text=1,t.Read=2,t.Write=3})(Ie||(Ie={}));var Le;(function(t){function r(a,n){let e={range:a};return s.number(n)&&(e.kind=n),e}t.create=r})(Le||(Le={}));var Me;(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(Me||(Me={}));var Pe;(function(t){t.Deprecated=1})(Pe||(Pe={}));var Ze;(function(t){function r(a,n,e,x,V){let de={name:a,kind:n,location:{uri:x,range:e}};return V&&(de.containerName=V),de}t.create=r})(Ze||(Ze={}));var Re;(function(t){function r(a,n,e,x){return x!==void 0?{name:a,kind:n,location:{uri:e,range:x}}:{name:a,kind:n,location:{uri:e}}}t.create=r})(Re||(Re={}));var We;(function(t){function r(n,e,x,V,de,xe){let Ae={name:n,detail:e,kind:x,range:V,selectionRange:de};return xe!==void 0&&(Ae.children=xe),Ae}t.create=r;function a(n){let e=n;return e&&s.string(e.name)&&s.number(e.kind)&&W.is(e.range)&&W.is(e.selectionRange)&&(e.detail===void 0||s.string(e.detail))&&(e.deprecated===void 0||s.boolean(e.deprecated))&&(e.children===void 0||Array.isArray(e.children))&&(e.tags===void 0||Array.isArray(e.tags))}t.is=a})(We||(We={}));var $e;(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})($e||($e={}));var je;(function(t){t.Invoked=1,t.Automatic=2})(je||(je={}));var qe;(function(t){function r(n,e,x){let V={diagnostics:n};return e!=null&&(V.only=e),x!=null&&(V.triggerKind=x),V}t.create=r;function a(n){let e=n;return s.defined(e)&&s.typedArray(e.diagnostics,k.is)&&(e.only===void 0||s.typedArray(e.only,s.string))&&(e.triggerKind===void 0||e.triggerKind===je.Invoked||e.triggerKind===je.Automatic)}t.is=a})(qe||(qe={}));var He;(function(t){function r(n,e,x){let V={title:n},de=!0;return typeof e=="string"?(de=!1,V.kind=e):T.is(e)?V.command=e:V.edit=e,de&&x!==void 0&&(V.kind=x),V}t.create=r;function a(n){let e=n;return e&&s.string(e.title)&&(e.diagnostics===void 0||s.typedArray(e.diagnostics,k.is))&&(e.kind===void 0||s.string(e.kind))&&(e.edit!==void 0||e.command!==void 0)&&(e.command===void 0||T.is(e.command))&&(e.isPreferred===void 0||s.boolean(e.isPreferred))&&(e.edit===void 0||U.is(e.edit))}t.is=a})(He||(He={}));var Be;(function(t){function r(n,e){let x={range:n};return s.defined(e)&&(x.data=e),x}t.create=r;function a(n){let e=n;return s.defined(e)&&W.is(e.range)&&(s.undefined(e.command)||T.is(e.command))}t.is=a})(Be||(Be={}));var Ne;(function(t){function r(n,e){return{tabSize:n,insertSpaces:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.uinteger(e.tabSize)&&s.boolean(e.insertSpaces)}t.is=a})(Ne||(Ne={}));var De;(function(t){function r(n,e,x){return{range:n,target:e,data:x}}t.create=r;function a(n){let e=n;return s.defined(e)&&W.is(e.range)&&(s.undefined(e.target)||s.string(e.target))}t.is=a})(De||(De={}));var ze;(function(t){function r(n,e){return{range:n,parent:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.range)&&(e.parent===void 0||t.is(e.parent))}t.is=a})(ze||(ze={}));var Ve;(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(Ve||(Ve={}));var u;(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(u||(u={}));var S;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}t.is=r})(S||(S={}));var E;(function(t){function r(n,e){return{range:n,text:e}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&s.string(e.text)}t.is=a})(E||(E={}));var R;(function(t){function r(n,e,x){return{range:n,variableName:e,caseSensitiveLookup:x}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&s.boolean(e.caseSensitiveLookup)&&(s.string(e.variableName)||e.variableName===void 0)}t.is=a})(R||(R={}));var Z;(function(t){function r(n,e){return{range:n,expression:e}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&(s.string(e.expression)||e.expression===void 0)}t.is=a})(Z||(Z={}));var re;(function(t){function r(n,e){return{frameId:n,stoppedLocation:e}}t.create=r;function a(n){const e=n;return s.defined(e)&&W.is(n.stoppedLocation)}t.is=a})(re||(re={}));var ce;(function(t){t.Type=1,t.Parameter=2;function r(a){return a===1||a===2}t.is=r})(ce||(ce={}));var ue;(function(t){function r(n){return{value:n}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&(e.tooltip===void 0||s.string(e.tooltip)||fe.is(e.tooltip))&&(e.location===void 0||N.is(e.location))&&(e.command===void 0||T.is(e.command))}t.is=a})(ue||(ue={}));var he;(function(t){function r(n,e,x){const V={position:n,label:e};return x!==void 0&&(V.kind=x),V}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&j.is(e.position)&&(s.string(e.label)||s.typedArray(e.label,ue.is))&&(e.kind===void 0||ce.is(e.kind))&&e.textEdits===void 0||s.typedArray(e.textEdits,v.is)&&(e.tooltip===void 0||s.string(e.tooltip)||fe.is(e.tooltip))&&(e.paddingLeft===void 0||s.boolean(e.paddingLeft))&&(e.paddingRight===void 0||s.boolean(e.paddingRight))}t.is=a})(he||(he={}));var we;(function(t){function r(a){return{kind:"snippet",value:a}}t.createSnippet=r})(we||(we={}));var Oe;(function(t){function r(a,n,e,x){return{insertText:a,filterText:n,range:e,command:x}}t.create=r})(Oe||(Oe={}));var Ue;(function(t){function r(a){return{items:a}}t.create=r})(Ue||(Ue={}));var l;(function(t){t.Invoked=0,t.Automatic=1})(l||(l={}));var h;(function(t){function r(a,n){return{range:a,text:n}}t.create=r})(h||(h={}));var y;(function(t){function r(a,n){return{triggerKind:a,selectedCompletionInfo:n}}t.create=r})(y||(y={}));var $;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&F.is(n.uri)&&s.string(n.name)}t.is=r})($||($={}));const Y=null;var ie;(function(t){function r(x,V,de,xe){return new ne(x,V,de,xe)}t.create=r;function a(x){let V=x;return!!(s.defined(V)&&s.string(V.uri)&&(s.undefined(V.languageId)||s.string(V.languageId))&&s.uinteger(V.lineCount)&&s.func(V.getText)&&s.func(V.positionAt)&&s.func(V.offsetAt))}t.is=a;function n(x,V){let de=x.getText(),xe=e(V,(ke,Te)=>{let Fe=ke.range.start.line-Te.range.start.line;return Fe===0?ke.range.start.character-Te.range.start.character:Fe}),Ae=de.length;for(let ke=xe.length-1;ke>=0;ke--){let Te=xe[ke],Fe=x.offsetAt(Te.range.start),ve=x.offsetAt(Te.range.end);if(ve<=Ae)de=de.substring(0,Fe)+Te.newText+de.substring(ve,de.length);else throw new Error("Overlapping edit");Ae=Fe}return de}t.applyEdits=n;function e(x,V){if(x.length<=1)return x;const de=x.length/2|0,xe=x.slice(0,de),Ae=x.slice(de);e(xe,V),e(Ae,V);let ke=0,Te=0,Fe=0;for(;ke<xe.length&&Te<Ae.length;)V(xe[ke],Ae[Te])<=0?x[Fe++]=xe[ke++]:x[Fe++]=Ae[Te++];for(;ke<xe.length;)x[Fe++]=xe[ke++];for(;Te<Ae.length;)x[Fe++]=Ae[Te++];return x}})(ie||(ie={}));class ne{constructor(r,a,n,e){this._uri=r,this._languageId=a,this._version=n,this._content=e,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(r){if(r){let a=this.offsetAt(r.start),n=this.offsetAt(r.end);return this._content.substring(a,n)}return this._content}update(r,a){this._content=r.text,this._version=a,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let r=[],a=this._content,n=!0;for(let e=0;e<a.length;e++){n&&(r.push(e),n=!1);let x=a.charAt(e);n=x==="\r"||x===`
`,x==="\r"&&e+1<a.length&&a.charAt(e+1)===`
`&&e++}n&&a.length>0&&r.push(a.length),this._lineOffsets=r}return this._lineOffsets}positionAt(r){r=Math.max(Math.min(r,this._content.length),0);let a=this.getLineOffsets(),n=0,e=a.length;if(e===0)return j.create(0,r);for(;n<e;){let V=Math.floor((n+e)/2);a[V]>r?e=V:n=V+1}let x=n-1;return j.create(x,r-a[x])}offsetAt(r){let a=this.getLineOffsets();if(r.line>=a.length)return this._content.length;if(r.line<0)return 0;let n=a[r.line],e=r.line+1<a.length?a[r.line+1]:this._content.length;return Math.max(Math.min(n+r.character,e),n)}get lineCount(){return this.getLineOffsets().length}}var s;(function(t){const r=Object.prototype.toString;function a(ve){return typeof ve!="undefined"}t.defined=a;function n(ve){return typeof ve=="undefined"}t.undefined=n;function e(ve){return ve===!0||ve===!1}t.boolean=e;function x(ve){return r.call(ve)==="[object String]"}t.string=x;function V(ve){return r.call(ve)==="[object Number]"}t.number=V;function de(ve,Je,Ye){return r.call(ve)==="[object Number]"&&Je<=ve&&ve<=Ye}t.numberRange=de;function xe(ve){return r.call(ve)==="[object Number]"&&-2147483648<=ve&&ve<=2147483647}t.integer=xe;function Ae(ve){return r.call(ve)==="[object Number]"&&0<=ve&&ve<=2147483647}t.uinteger=Ae;function ke(ve){return r.call(ve)==="[object Function]"}t.func=ke;function Te(ve){return ve!==null&&typeof ve=="object"}t.objectLiteral=Te;function Fe(ve,Je){return Array.isArray(ve)&&ve.every(Je)}t.typedArray=Fe})(s||(s={}))},43545:function(be,Q,M){"use strict";M.d(Q,{c:function(){return P},o:function(){return K}});var ee=M(73656),F;(()=>{"use strict";var j={470:_=>{function O(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function g(o,m){for(var p,b="",d=0,k=-1,T=0,v=0;v<=o.length;++v){if(v<o.length)p=o.charCodeAt(v);else{if(p===47)break;p=47}if(p===47){if(!(k===v-1||T===1))if(k!==v-1&&T===2){if(b.length<2||d!==2||b.charCodeAt(b.length-1)!==46||b.charCodeAt(b.length-2)!==46){if(b.length>2){var c=b.lastIndexOf("/");if(c!==b.length-1){c===-1?(b="",d=0):d=(b=b.slice(0,c)).length-1-b.lastIndexOf("/"),k=v,T=0;continue}}else if(b.length===2||b.length===1){b="",d=0,k=v,T=0;continue}}m&&(b.length>0?b+="/..":b="..",d=2)}else b.length>0?b+="/"+o.slice(k+1,v):b=o.slice(k+1,v),d=v-k-1;k=v,T=0}else p===46&&T!==-1?++T:T=-1}return b}var w={resolve:function(){for(var o,m="",p=!1,b=arguments.length-1;b>=-1&&!p;b--){var d;b>=0?d=arguments[b]:(o===void 0&&(o=ee.cwd()),d=o),O(d),d.length!==0&&(m=d+"/"+m,p=d.charCodeAt(0)===47)}return m=g(m,!p),p?m.length>0?"/"+m:"/":m.length>0?m:"."},normalize:function(o){if(O(o),o.length===0)return".";var m=o.charCodeAt(0)===47,p=o.charCodeAt(o.length-1)===47;return(o=g(o,!m)).length!==0||m||(o="."),o.length>0&&p&&(o+="/"),m?"/"+o:o},isAbsolute:function(o){return O(o),o.length>0&&o.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var o,m=0;m<arguments.length;++m){var p=arguments[m];O(p),p.length>0&&(o===void 0?o=p:o+="/"+p)}return o===void 0?".":w.normalize(o)},relative:function(o,m){if(O(o),O(m),o===m||(o=w.resolve(o))===(m=w.resolve(m)))return"";for(var p=1;p<o.length&&o.charCodeAt(p)===47;++p);for(var b=o.length,d=b-p,k=1;k<m.length&&m.charCodeAt(k)===47;++k);for(var T=m.length-k,v=d<T?d:T,c=-1,i=0;i<=v;++i){if(i===v){if(T>v){if(m.charCodeAt(k+i)===47)return m.slice(k+i+1);if(i===0)return m.slice(k+i)}else d>v&&(o.charCodeAt(p+i)===47?c=i:i===0&&(c=0));break}var f=o.charCodeAt(p+i);if(f!==m.charCodeAt(k+i))break;f===47&&(c=i)}var C="";for(i=p+c+1;i<=b;++i)i!==b&&o.charCodeAt(i)!==47||(C.length===0?C+="..":C+="/..");return C.length>0?C+m.slice(k+c):(k+=c,m.charCodeAt(k)===47&&++k,m.slice(k))},_makeLong:function(o){return o},dirname:function(o){if(O(o),o.length===0)return".";for(var m=o.charCodeAt(0),p=m===47,b=-1,d=!0,k=o.length-1;k>=1;--k)if((m=o.charCodeAt(k))===47){if(!d){b=k;break}}else d=!1;return b===-1?p?"/":".":p&&b===1?"//":o.slice(0,b)},basename:function(o,m){if(m!==void 0&&typeof m!="string")throw new TypeError('"ext" argument must be a string');O(o);var p,b=0,d=-1,k=!0;if(m!==void 0&&m.length>0&&m.length<=o.length){if(m.length===o.length&&m===o)return"";var T=m.length-1,v=-1;for(p=o.length-1;p>=0;--p){var c=o.charCodeAt(p);if(c===47){if(!k){b=p+1;break}}else v===-1&&(k=!1,v=p+1),T>=0&&(c===m.charCodeAt(T)?--T==-1&&(d=p):(T=-1,d=v))}return b===d?d=v:d===-1&&(d=o.length),o.slice(b,d)}for(p=o.length-1;p>=0;--p)if(o.charCodeAt(p)===47){if(!k){b=p+1;break}}else d===-1&&(k=!1,d=p+1);return d===-1?"":o.slice(b,d)},extname:function(o){O(o);for(var m=-1,p=0,b=-1,d=!0,k=0,T=o.length-1;T>=0;--T){var v=o.charCodeAt(T);if(v!==47)b===-1&&(d=!1,b=T+1),v===46?m===-1?m=T:k!==1&&(k=1):m!==-1&&(k=-1);else if(!d){p=T+1;break}}return m===-1||b===-1||k===0||k===1&&m===b-1&&m===p+1?"":o.slice(m,b)},format:function(o){if(o===null||typeof o!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof o);return function(m,p){var b=p.dir||p.root,d=p.base||(p.name||"")+(p.ext||"");return b?b===p.root?b+d:b+"/"+d:d}(0,o)},parse:function(o){O(o);var m={root:"",dir:"",base:"",ext:"",name:""};if(o.length===0)return m;var p,b=o.charCodeAt(0),d=b===47;d?(m.root="/",p=1):p=0;for(var k=-1,T=0,v=-1,c=!0,i=o.length-1,f=0;i>=p;--i)if((b=o.charCodeAt(i))!==47)v===-1&&(c=!1,v=i+1),b===46?k===-1?k=i:f!==1&&(f=1):k!==-1&&(f=-1);else if(!c){T=i+1;break}return k===-1||v===-1||f===0||f===1&&k===v-1&&k===T+1?v!==-1&&(m.base=m.name=T===0&&d?o.slice(1,v):o.slice(T,v)):(T===0&&d?(m.name=o.slice(1,k),m.base=o.slice(1,v)):(m.name=o.slice(T,k),m.base=o.slice(T,v)),m.ext=o.slice(k,v)),T>0?m.dir=o.slice(0,T-1):d&&(m.dir="/"),m},sep:"/",delimiter:":",win32:null,posix:null};w.posix=w,_.exports=w}},W={};function N(_){var O=W[_];if(O!==void 0)return O.exports;var g=W[_]={exports:{}};return j[_](g,g.exports,N),g.exports}N.d=(_,O)=>{for(var g in O)N.o(O,g)&&!N.o(_,g)&&Object.defineProperty(_,g,{enumerable:!0,get:O[g]})},N.o=(_,O)=>Object.prototype.hasOwnProperty.call(_,O),N.r=_=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(_,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(_,"__esModule",{value:!0})};var A={};(()=>{let _;N.r(A),N.d(A,{URI:()=>d,Utils:()=>ae}),typeof ee=="object"?_=ee.platform==="win32":typeof navigator=="object"&&(_=navigator.userAgent.indexOf("Windows")>=0);const O=/^\w[\w\d+.-]*$/,g=/^\//,w=/^\/\//;function o(q,L){if(!q.scheme&&L)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${q.authority}", path: "${q.path}", query: "${q.query}", fragment: "${q.fragment}"}`);if(q.scheme&&!O.test(q.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(q.path){if(q.authority){if(!g.test(q.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(w.test(q.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const m="",p="/",b=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{constructor(L,J,te,ye,fe,oe=!1){Ge(this,"scheme");Ge(this,"authority");Ge(this,"path");Ge(this,"query");Ge(this,"fragment");typeof L=="object"?(this.scheme=L.scheme||m,this.authority=L.authority||m,this.path=L.path||m,this.query=L.query||m,this.fragment=L.fragment||m):(this.scheme=function(B,X){return B||X?B:"file"}(L,oe),this.authority=J||m,this.path=function(B,X){switch(B){case"https":case"http":case"file":X?X[0]!==p&&(X=p+X):X=p}return X}(this.scheme,te||m),this.query=ye||m,this.fragment=fe||m,o(this,oe))}static isUri(L){return L instanceof d||!!L&&typeof L.authority=="string"&&typeof L.fragment=="string"&&typeof L.path=="string"&&typeof L.query=="string"&&typeof L.scheme=="string"&&typeof L.fsPath=="string"&&typeof L.with=="function"&&typeof L.toString=="function"}get fsPath(){return f(this,!1)}with(L){if(!L)return this;let{scheme:J,authority:te,path:ye,query:fe,fragment:oe}=L;return J===void 0?J=this.scheme:J===null&&(J=m),te===void 0?te=this.authority:te===null&&(te=m),ye===void 0?ye=this.path:ye===null&&(ye=m),fe===void 0?fe=this.query:fe===null&&(fe=m),oe===void 0?oe=this.fragment:oe===null&&(oe=m),J===this.scheme&&te===this.authority&&ye===this.path&&fe===this.query&&oe===this.fragment?this:new T(J,te,ye,fe,oe)}static parse(L,J=!1){const te=b.exec(L);return te?new T(te[2]||m,G(te[4]||m),G(te[5]||m),G(te[7]||m),G(te[9]||m),J):new T(m,m,m,m,m)}static file(L){let J=m;if(_&&(L=L.replace(/\\/g,p)),L[0]===p&&L[1]===p){const te=L.indexOf(p,2);te===-1?(J=L.substring(2),L=p):(J=L.substring(2,te),L=L.substring(te)||p)}return new T("file",J,L,m,m)}static from(L){const J=new T(L.scheme,L.authority,L.path,L.query,L.fragment);return o(J,!0),J}toString(L=!1){return C(this,L)}toJSON(){return this}static revive(L){if(L){if(L instanceof d)return L;{const J=new T(L);return J._formatted=L.external,J._fsPath=L._sep===k?L.fsPath:null,J}}return L}}const k=_?1:void 0;class T extends d{constructor(){super(...arguments);Ge(this,"_formatted",null);Ge(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=f(this,!1)),this._fsPath}toString(J=!1){return J?C(this,!0):(this._formatted||(this._formatted=C(this,!1)),this._formatted)}toJSON(){const J={$mid:1};return this._fsPath&&(J.fsPath=this._fsPath,J._sep=k),this._formatted&&(J.external=this._formatted),this.path&&(J.path=this.path),this.scheme&&(J.scheme=this.scheme),this.authority&&(J.authority=this.authority),this.query&&(J.query=this.query),this.fragment&&(J.fragment=this.fragment),J}}const v={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function c(q,L,J){let te,ye=-1;for(let fe=0;fe<q.length;fe++){const oe=q.charCodeAt(fe);if(oe>=97&&oe<=122||oe>=65&&oe<=90||oe>=48&&oe<=57||oe===45||oe===46||oe===95||oe===126||L&&oe===47||J&&oe===91||J&&oe===93||J&&oe===58)ye!==-1&&(te+=encodeURIComponent(q.substring(ye,fe)),ye=-1),te!==void 0&&(te+=q.charAt(fe));else{te===void 0&&(te=q.substr(0,fe));const B=v[oe];B!==void 0?(ye!==-1&&(te+=encodeURIComponent(q.substring(ye,fe)),ye=-1),te+=B):ye===-1&&(ye=fe)}}return ye!==-1&&(te+=encodeURIComponent(q.substring(ye))),te!==void 0?te:q}function i(q){let L;for(let J=0;J<q.length;J++){const te=q.charCodeAt(J);te===35||te===63?(L===void 0&&(L=q.substr(0,J)),L+=v[te]):L!==void 0&&(L+=q[J])}return L!==void 0?L:q}function f(q,L){let J;return J=q.authority&&q.path.length>1&&q.scheme==="file"?`//${q.authority}${q.path}`:q.path.charCodeAt(0)===47&&(q.path.charCodeAt(1)>=65&&q.path.charCodeAt(1)<=90||q.path.charCodeAt(1)>=97&&q.path.charCodeAt(1)<=122)&&q.path.charCodeAt(2)===58?L?q.path.substr(1):q.path[1].toLowerCase()+q.path.substr(2):q.path,_&&(J=J.replace(/\//g,"\\")),J}function C(q,L){const J=L?i:c;let te="",{scheme:ye,authority:fe,path:oe,query:B,fragment:X}=q;if(ye&&(te+=ye,te+=":"),(fe||ye==="file")&&(te+=p,te+=p),fe){let se=fe.indexOf("@");if(se!==-1){const le=fe.substr(0,se);fe=fe.substr(se+1),se=le.lastIndexOf(":"),se===-1?te+=J(le,!1,!1):(te+=J(le.substr(0,se),!1,!1),te+=":",te+=J(le.substr(se+1),!1,!0)),te+="@"}fe=fe.toLowerCase(),se=fe.lastIndexOf(":"),se===-1?te+=J(fe,!1,!0):(te+=J(fe.substr(0,se),!1,!0),te+=fe.substr(se))}if(oe){if(oe.length>=3&&oe.charCodeAt(0)===47&&oe.charCodeAt(2)===58){const se=oe.charCodeAt(1);se>=65&&se<=90&&(oe=`/${String.fromCharCode(se+32)}:${oe.substr(3)}`)}else if(oe.length>=2&&oe.charCodeAt(1)===58){const se=oe.charCodeAt(0);se>=65&&se<=90&&(oe=`${String.fromCharCode(se+32)}:${oe.substr(2)}`)}te+=J(oe,!0,!1)}return B&&(te+="?",te+=J(B,!1,!1)),X&&(te+="#",te+=L?X:c(X,!1,!1)),te}function I(q){try{return decodeURIComponent(q)}catch(L){return q.length>3?q.substr(0,3)+I(q.substr(3)):q}}const D=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function G(q){return q.match(D)?q.replace(D,L=>I(L)):q}var U=N(470);const z=U.posix||U,H="/";var ae;(function(q){q.joinPath=function(L,...J){return L.with({path:z.join(L.path,...J)})},q.resolvePath=function(L,...J){let te=L.path,ye=!1;te[0]!==H&&(te=H+te,ye=!0);let fe=z.resolve(te,...J);return ye&&fe[0]===H&&!L.authority&&(fe=fe.substring(1)),L.with({path:fe})},q.dirname=function(L){if(L.path.length===0||L.path===H)return L;let J=z.dirname(L.path);return J.length===1&&J.charCodeAt(0)===46&&(J=""),L.with({path:J})},q.basename=function(L){return z.basename(L.path)},q.extname=function(L){return z.extname(L.path)}})(ae||(ae={}))})(),F=A})();const{URI:K,Utils:P}=F}}]);
}());