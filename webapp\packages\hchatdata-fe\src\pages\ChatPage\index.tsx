import React, { Suspense, lazy } from 'react';
import { Spin } from 'antd';

// 动态导入Chat组件 - 修复导入方式
const Chat = lazy(() => 
  import('hchatdata-chat-sdk').then(module => ({ 
    default: module.Chat 
  }))
);

// 加载状态组件
const LoadingComponent = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <Spin size="large" tip="正在加载...">
      <div style={{ minHeight: '200px' }} />
    </Spin>
  </div>
);

const ChatPage: React.FC = () => {
  return (
    <div style={{ height: 'calc(100vh - 66px)', minHeight: 'calc(100vh - 66px)' }}>
      <Suspense fallback={<LoadingComponent />}>
        <Chat />
      </Suspense>
    </div>
  );
};

export default ChatPage;
