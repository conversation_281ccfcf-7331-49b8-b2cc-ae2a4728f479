import React, { useEffect } from 'react';
import { CheckCircleFilled } from '@ant-design/icons';
import { PREFIX_CLS } from '../../common/constants';

const prefixCls = `${PREFIX_CLS}-item`;

type Props = {
  data_query_type: string;
  onComplete?: () => void;
};

const IntentTip: React.FC<Props> = ({ data_query_type, onComplete }) => {
  useEffect(() => {
    // 模拟意图识别完成，立即调用完成回调
    if (onComplete) {
      onComplete();
    }
  }, [onComplete]);
  return (
    <div className={`${prefixCls}-parse-tip`}>
      <div className={`${prefixCls}-title-bar`}>
        <CheckCircleFilled className={`${prefixCls}-step-icon`} />
        <div className={`${prefixCls}-step-title`}>
          意图识别
        </div>
      </div>
      <div className={`${prefixCls}-content-container`}>
        <div className={`${prefixCls}-tip`}>
          <div className={`${prefixCls}-tip-content`}>
            <div className={`${prefixCls}-tip-item`}>
              用户的问题属于{data_query_type}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntentTip;
